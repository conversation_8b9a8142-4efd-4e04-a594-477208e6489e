#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试合并相同服务名的邮件报表功能
"""

import sys
import os
import tempfile
from datetime import datetime, timedelta

# 模拟数据结构
def create_test_data():
    """创建测试数据"""
    today = datetime.now().date()
    
    # 创建CPU测试数据
    cpu_results_table = []
    for i in range(3):  # 3天数据
        date = today - timedelta(days=i)
        date_str = date.strftime('%Y-%m-%d')
        
        # 为gray和prod环境创建不同的数据
        for namespace in ['gray', 'prod']:
            cpu_results_table.append({
                'service_name': 'abc-cis-sc-clinic-service',
                'namespace': namespace,
                'date': date_str,
                'avg_cpu_usage': 0.3 + (0.2 if namespace == 'prod' else 0.1) + (i * 0.05),
                'max_cpu_usage': 0.5 + (0.3 if namespace == 'prod' else 0.2) + (i * 0.05),
                'min_cpu_usage': 0.1 + (0.1 if namespace == 'prod' else 0.05) + (i * 0.02),
                'data_points': 120
            })
            
            cpu_results_table.append({
                'service_name': 'abc-cis-registration-service',
                'namespace': namespace,
                'date': date_str,
                'avg_cpu_usage': 0.4 + (0.15 if namespace == 'prod' else 0.08) + (i * 0.03),
                'max_cpu_usage': 0.6 + (0.25 if namespace == 'prod' else 0.15) + (i * 0.04),
                'min_cpu_usage': 0.2 + (0.08 if namespace == 'prod' else 0.03) + (i * 0.01),
                'data_points': 120
            })
    
    # 创建响应时间测试数据
    rt_results_table = []
    for i in range(3):  # 3天数据
        date = today - timedelta(days=i)
        date_str = date.strftime('%Y-%m-%d')
        
        for namespace in ['gray', 'prod']:
            rt_results_table.append({
                'service_name': 'abc-cis-sc-clinic-service',
                'namespace': namespace,
                'interface_name': '批量查询employee(/rpc/v3/clinics/employees/query-by-ids)',
                'date': date_str,
                'avg_response_time_ms': 200 + (50 if namespace == 'prod' else 30) + (i * 10),
                'max_response_time_ms': 400 + (100 if namespace == 'prod' else 60) + (i * 15),
                'min_response_time_ms': 100 + (20 if namespace == 'prod' else 10) + (i * 5),
                'data_points': 120
            })
            
            rt_results_table.append({
                'service_name': 'abc-cis-registration-service',
                'namespace': namespace,
                'interface_name': '挂号(/api/v2/registrations/manage)',
                'date': date_str,
                'avg_response_time_ms': 150 + (40 if namespace == 'prod' else 25) + (i * 8),
                'max_response_time_ms': 300 + (80 if namespace == 'prod' else 50) + (i * 12),
                'min_response_time_ms': 80 + (15 if namespace == 'prod' else 8) + (i * 3),
                'data_points': 120
            })
    
    return cpu_results_table, rt_results_table

def generate_test_html():
    """生成测试HTML"""
    cpu_data, rt_data = create_test_data()
    
    # 模拟HTML生成逻辑的核心部分
    all_dates = sorted(set(result['date'] for result in cpu_data))
    
    # 按服务名和namespace组织CPU数据
    cpu_by_service_ns = {}
    for result in cpu_data:
        service_name = result['service_name']
        namespace = result['namespace']
        key = (service_name, namespace)
        if key not in cpu_by_service_ns:
            cpu_by_service_ns[key] = {'service_name': service_name, 'namespace': namespace, 'dates': {}}
        cpu_by_service_ns[key]['dates'][result['date']] = result

    # 处理响应时间数据
    response_time_by_interface_ns = {}
    for result in rt_data:
        key = (result['service_name'], result['interface_name'], result['namespace'])
        if key not in response_time_by_interface_ns:
            response_time_by_interface_ns[key] = {
                'service_name': result['service_name'],
                'interface_name': result['interface_name'],
                'namespace': result['namespace'],
                'dates': {}
            }
        response_time_by_interface_ns[key]['dates'][result['date']] = result
    
    # 生成简化的HTML
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>监控报表测试</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            table { border-collapse: collapse; width: 100%; margin: 20px 0; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
            th { background-color: #4CAF50; color: white; }
            .service-name { text-align: left; font-weight: bold; }
            .namespace-sub { background-color: #e8f5e8; font-size: 11px; }
            .namespace-gray { background-color: #f0f0f0; }
            .namespace-prod { background-color: #fff; }
            .cpu-high { background-color: #ffebee; color: #d32f2f; font-weight: bold; }
            .cpu-medium { background-color: #fff3e0; color: #f57c00; font-weight: bold; }
            .cpu-low { background-color: #e8f5e8; color: #388e3c; }
            .rt-slow { background-color: #ffebee; color: #d32f2f; font-weight: bold; }
            .rt-medium { background-color: #fff3e0; color: #f57c00; font-weight: bold; }
            .rt-fast { background-color: #e8f5e8; color: #388e3c; }
        </style>
    </head>
    <body>
        <h1>监控报表 - 合并相同服务名测试</h1>
        
        <h2>🖥️ CPU使用率（最近3天，环境合并显示）</h2>
        <table>
            <tr>
                <th class='service-name'>服务名</th>
    """
    
    # 添加日期列
    for date_str in all_dates:
        html_content += f"<th colspan='2'>{date_str}<br><span style='font-size: 11px;'>Gray / Prod</span></th>"
    
    html_content += "<th colspan='2'>变化幅度<br>(最近2天)<br>Gray / Prod</th></tr>"
    
    # 添加子表头行
    html_content += "<tr><th></th>"
    for date_str in all_dates:
        html_content += "<th class='namespace-sub'>Gray</th><th class='namespace-sub'>Prod</th>"
    html_content += "<th class='namespace-sub'>Gray</th><th class='namespace-sub'>Prod</th></tr>"
    
    # 按服务名分组显示数据
    services = set(service_name for service_name, namespace in cpu_by_service_ns.keys())
    for service_name in sorted(services):
        html_content += f"<tr><td class='service-name'>{service_name}</td>"
        
        # 添加每个日期的CPU数据
        for date_str in all_dates:
            # Gray环境数据
            gray_key = (service_name, 'gray')
            if gray_key in cpu_by_service_ns and date_str in cpu_by_service_ns[gray_key]['dates']:
                cpu_data_item = cpu_by_service_ns[gray_key]['dates'][date_str]
                avg_cpu = cpu_data_item['avg_cpu_usage']
                max_cpu = cpu_data_item['max_cpu_usage']
                min_cpu = cpu_data_item['min_cpu_usage']
                
                css_class = "cpu-low namespace-gray"
                if avg_cpu > 0.8:
                    css_class = "cpu-high namespace-gray"
                elif avg_cpu > 0.5:
                    css_class = "cpu-medium namespace-gray"
                
                cpu_display = f"<span style='font-weight: bold;'>{avg_cpu:.4f}</span><br><span style='font-size: 11px;'>({max_cpu:.4f}/{min_cpu:.4f})</span>"
                html_content += f"<td class='{css_class}'>{cpu_display}</td>"
            else:
                html_content += "<td class='namespace-gray'>-</td>"
            
            # Prod环境数据
            prod_key = (service_name, 'prod')
            if prod_key in cpu_by_service_ns and date_str in cpu_by_service_ns[prod_key]['dates']:
                cpu_data_item = cpu_by_service_ns[prod_key]['dates'][date_str]
                avg_cpu = cpu_data_item['avg_cpu_usage']
                max_cpu = cpu_data_item['max_cpu_usage']
                min_cpu = cpu_data_item['min_cpu_usage']
                
                css_class = "cpu-low namespace-prod"
                if avg_cpu > 0.8:
                    css_class = "cpu-high namespace-prod"
                elif avg_cpu > 0.5:
                    css_class = "cpu-medium namespace-prod"
                
                cpu_display = f"<span style='font-weight: bold;'>{avg_cpu:.4f}</span><br><span style='font-size: 11px;'>({max_cpu:.4f}/{min_cpu:.4f})</span>"
                html_content += f"<td class='{css_class}'>{cpu_display}</td>"
            else:
                html_content += "<td class='namespace-prod'>-</td>"
        
        # 添加变化幅度（简化版）
        html_content += "<td class='namespace-gray'>⬇️ 5.2%</td>"
        html_content += "<td class='namespace-prod'>⬆️ 3.1%</td>"
        html_content += "</tr>"
    
    html_content += """
        </table>
        
        <h2>⚡ 响应时间（最近3天，环境合并显示）</h2>
        <table>
            <tr>
                <th class='service-name'>服务名</th>
                <th>接口功能</th>
    """
    
    # 添加日期列
    for date_str in all_dates:
        html_content += f"<th colspan='2'>{date_str}<br><span style='font-size: 11px;'>Gray / Prod (ms)</span></th>"
    
    html_content += "<th colspan='2'>变化幅度<br>(最近2天)<br>Gray / Prod</th></tr>"
    
    # 添加子表头行
    html_content += "<tr><th></th><th></th>"
    for date_str in all_dates:
        html_content += "<th class='namespace-sub'>Gray</th><th class='namespace-sub'>Prod</th>"
    html_content += "<th class='namespace-sub'>Gray</th><th class='namespace-sub'>Prod</th></tr>"
    
    # 按服务名和接口分组显示数据
    service_interfaces = set((service_name, interface_name) for service_name, interface_name, namespace in response_time_by_interface_ns.keys())
    for service_name, interface_name in sorted(service_interfaces):
        html_content += f"<tr><td class='service-name'>{service_name}</td><td style='text-align: left;'>{interface_name}</td>"
        
        # 添加每个日期的响应时间数据
        for date_str in all_dates:
            # Gray环境数据
            gray_key = (service_name, interface_name, 'gray')
            if gray_key in response_time_by_interface_ns and date_str in response_time_by_interface_ns[gray_key]['dates']:
                rt_data_item = response_time_by_interface_ns[gray_key]['dates'][date_str]
                avg_rt = rt_data_item['avg_response_time_ms']
                
                css_class = "rt-fast namespace-gray"
                if avg_rt > 1000:
                    css_class = "rt-slow namespace-gray"
                elif avg_rt > 500:
                    css_class = "rt-medium namespace-gray"
                
                html_content += f"<td class='{css_class}' title='平均: {avg_rt:.2f}ms'>{avg_rt:.2f}</td>"
            else:
                html_content += "<td class='namespace-gray'>-</td>"
            
            # Prod环境数据
            prod_key = (service_name, interface_name, 'prod')
            if prod_key in response_time_by_interface_ns and date_str in response_time_by_interface_ns[prod_key]['dates']:
                rt_data_item = response_time_by_interface_ns[prod_key]['dates'][date_str]
                avg_rt = rt_data_item['avg_response_time_ms']
                
                css_class = "rt-fast namespace-prod"
                if avg_rt > 1000:
                    css_class = "rt-slow namespace-prod"
                elif avg_rt > 500:
                    css_class = "rt-medium namespace-prod"
                
                html_content += f"<td class='{css_class}' title='平均: {avg_rt:.2f}ms'>{avg_rt:.2f}</td>"
            else:
                html_content += "<td class='namespace-prod'>-</td>"
        
        # 添加变化幅度（简化版）
        html_content += "<td class='namespace-gray'>⬇️ 8.5%</td>"
        html_content += "<td class='namespace-prod'>⬆️ 12.3%</td>"
        html_content += "</tr>"
    
    html_content += """
        </table>
        
        <div style="margin-top: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
            <h3>📋 说明:</h3>
            <p><strong>表格格式:</strong> 相同服务名合并在一行，每个日期分Gray/Prod两列显示</p>
            <p><strong>环境说明:</strong> gray=灰度环境, prod=生产环境</p>
            <p><strong>CPU格式:</strong> 平均值(最大值/最小值)</p>
            <p><strong>颜色说明:</strong></p>
            <ul>
                <li>🔴 高CPU使用率(>0.8) / 慢响应(>1s)</li>
                <li>🟡 中等CPU使用率(>0.5) / 中等响应(>500ms)</li>
                <li>🟢 低CPU使用率(≤0.5) / 快响应(≤500ms)</li>
            </ul>
        </div>
    </body>
    </html>
    """
    
    return html_content

def main():
    print("生成合并相同服务名的邮件报表测试...")
    
    html_content = generate_test_html()
    
    # 保存HTML文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
        f.write(html_content)
        print(f"测试HTML报表已保存到: {f.name}")
        print("可以在浏览器中打开查看效果")
    
    print("\n修改要点:")
    print("1. ✅ 相同服务名合并在一行显示")
    print("2. ✅ 每个日期分为Gray/Prod两个子列")
    print("3. ✅ 添加了子表头行标识环境")
    print("4. ✅ 保持原有的颜色编码和状态指示")
    print("5. ✅ 变化幅度也按环境分别显示")
    print("6. ✅ 响应时间表格同样采用合并格式")

if __name__ == '__main__':
    main()
