#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试namespace分离显示的监控图表功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'scripts/common'))

from prometheus_monitor import PrometheusMonitor
from datetime import datetime, timedelta
import tempfile

def create_test_data():
    """创建测试数据"""
    today = datetime.now().date()
    
    # 创建CPU测试数据
    cpu_test_data = []
    for i in range(7):
        date = today - timedelta(days=i)
        date_str = date.strftime('%Y-%m-%d')
        
        # 为gray和prod环境创建不同的数据
        for namespace in ['gray', 'prod']:
            cpu_test_data.append({
                'service_name': 'abc-cis-sc-clinic-service',
                'namespace': namespace,
                'date': date_str,
                'avg_cpu_usage': 0.3 + (0.2 if namespace == 'prod' else 0.1) + (i * 0.05),
                'max_cpu_usage': 0.5 + (0.3 if namespace == 'prod' else 0.2) + (i * 0.05),
                'min_cpu_usage': 0.1 + (0.1 if namespace == 'prod' else 0.05) + (i * 0.02),
                'data_points': 120
            })
    
    # 创建响应时间测试数据
    rt_test_data = []
    for i in range(7):
        date = today - timedelta(days=i)
        date_str = date.strftime('%Y-%m-%d')
        
        for namespace in ['gray', 'prod']:
            rt_test_data.append({
                'service_name': 'abc-cis-sc-clinic-service',
                'namespace': namespace,
                'interface_name': 'batch_query_employee(/rpc/v3/clinics/employees/query-by-ids)',
                'date': date_str,
                'avg_response_time_ms': 200 + (50 if namespace == 'prod' else 30) + (i * 10),
                'max_response_time_ms': 400 + (100 if namespace == 'prod' else 60) + (i * 15),
                'min_response_time_ms': 100 + (20 if namespace == 'prod' else 10) + (i * 5),
                'data_points': 120
            })
    
    return cpu_test_data, rt_test_data

def test_namespace_charts():
    """测试namespace分离的图表生成"""
    print("Testing namespace separated monitoring charts...")
    
    # 创建监控实例（使用虚拟URL，因为我们只测试图表生成）
    monitor = PrometheusMonitor("http://test", "test_token")
    
    # 创建测试数据
    cpu_data, rt_data = create_test_data()
    
    print("Created {} CPU data points and {} response time data points".format(len(cpu_data), len(rt_data)))
    
    # 测试CPU图表生成
    print("\nTesting CPU chart generation...")
    cpu_chart = monitor.generate_cpu_chart(cpu_data, 'abc-cis-sc-clinic-service')
    if cpu_chart:
        print("CPU chart generated successfully")
        print("Chart data length: {} characters".format(len(cpu_chart)))
    else:
        print("CPU chart generation failed")
    
    # 测试响应时间图表生成
    print("\nTesting response time chart generation...")
    rt_chart = monitor.generate_response_time_chart(rt_data, 'abc-cis-sc-clinic-service')
    if rt_chart:
        print("Response time chart generated successfully")
        print("Chart data length: {} characters".format(len(rt_chart)))
    else:
        print("Response time chart generation failed")
    
    # 测试HTML报表生成
    print("\nTesting HTML report generation...")
    html_content = monitor.generate_combined_html_report(
        cpu_data[:6],  # 表格数据（3天）
        rt_data[:6],   # 表格数据（3天）
        cpu_data,      # 图表数据（7天）
        rt_data        # 图表数据（7天）
    )
    
    if html_content and len(html_content) > 1000:
        print("HTML report generated successfully")
        print("HTML content length: {} characters".format(len(html_content)))
        
        # 保存HTML文件用于查看
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
            f.write(html_content)
            print("HTML report saved to: {}".format(f.name))
            print("You can open it in a browser to view the results")
    else:
        print("HTML report generation failed")
    
    print("\nTest verification points:")
    print("1. Different namespaces use different colors and line styles")
    print("2. Gray environment uses solid lines and circle markers")
    print("3. Prod environment uses dashed lines and square markers")
    print("4. CPU charts show average, max, and min values")
    print("5. Response time charts are grouped by namespace")
    print("6. Legend descriptions updated for new display logic")
    
    print("\nFixed issues:")
    print("- Original issue: Mixed display of different namespace data")
    print("- Fixed: Each namespace displays independent curves")
    print("- Original issue: Cannot distinguish gray and prod environments")
    print("- Fixed: Use different colors, line styles, markers to distinguish")
    print("- Original issue: Inaccurate legend descriptions")
    print("- Fixed: Updated legend to reflect new logic")

if __name__ == '__main__':
    test_namespace_charts()
