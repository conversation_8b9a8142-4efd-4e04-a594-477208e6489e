# Prometheus监控脚本详细分析

## 📋 脚本概述

这是一个基于Prometheus的业务监控报表生成脚本，主要功能是：
- 从Prometheus获取CPU使用率和响应时间数据
- 生成可视化图表和HTML报表
- 支持邮件发送监控报表
- 按namespace（gray/prod环境）区分监控数据

## 🏗️ 整体架构

```
prometheus_monitor.py
├── 核心接口配置 (core_interfaces)
├── PrometheusMonitor类
│   ├── 数据查询模块
│   ├── 报表生成模块
│   ├── 图表生成模块
│   └── 邮件发送模块
└── 主函数 (main)
```

## 🔧 核心组件详解

### 1. 核心接口配置
```python
core_interfaces = [
    ["服务名", "接口功能", "接口路径", "HTTP方法"],
    # 包含诊所、挂号、门诊、收费等核心业务接口
]
```
**作用**: 定义需要监控的核心业务接口，用于响应时间监控

### 2. PrometheusMonitor类

#### 2.1 初始化
```python
def __init__(self, prometheus_url, token):
    self.prometheus_url = prometheus_url.rstrip('/')
    self.token = token
```
**作用**: 配置Prometheus连接信息

#### 2.2 数据查询模块

##### query_prometheus()
```python
def query_prometheus(self, query, start_time, end_time, step='60s'):
```
**功能**: 
- 向Prometheus发送PromQL查询
- 支持时间范围查询
- 返回JSON格式数据

**查询示例**:
- CPU: `process_cpu_usage{namespace="gray"}`
- 响应时间: `sum(rate(http_server_requests_seconds_sum{...}[5m])) / sum(rate(http_server_requests_seconds_count{...}[5m])) * 1000`

##### get_cpu_usage_report()
```python
def get_cpu_usage_report(self, days=2):
```
**功能**:
- 查询指定天数的CPU使用率数据
- 时间范围: 每天9:00-11:00
- 按(服务名, namespace, 日期)维度聚合
- 计算平均值、最大值、最小值

**数据流程**:
```
1. 遍历日期范围 → 2. 遍历namespace(gray/prod) → 3. 查询Prometheus
4. 聚合原始数据 → 5. 计算统计值 → 6. 返回结构化数据
```

##### get_response_time_report()
```python
def get_response_time_report(self, days=2):
```
**功能**:
- 查询核心接口响应时间数据
- 基于Spring Boot Micrometer指标
- 按(服务名, 接口名, namespace, 日期)维度聚合

#### 2.3 报表生成模块

##### print_report() / print_response_time_report()
**功能**: 生成控制台文本报表，包含状态图标(🔴🟡🟢)

##### generate_combined_html_report()
**功能**: 生成综合HTML报表
**特点**:
- 相同服务名合并显示
- 每个日期分Gray/Prod两列
- 包含CSS样式和颜色编码
- 支持变化趋势显示

**表格结构**:
```
| 服务名 | 日期1(Gray/Prod) | 日期2(Gray/Prod) | 变化幅度(Gray/Prod) |
|--------|------------------|------------------|---------------------|
| 服务A  | 数据/数据        | 数据/数据        | ⬇️5%/⬆️3%          |
```

#### 2.4 图表生成模块

##### generate_cpu_chart()
**功能**: 生成CPU使用率趋势图
**特点**:
- 按namespace分别显示曲线
- gray环境: 实线+圆点
- prod环境: 虚线+方块
- 包含平均值、最大值、最小值
- 填充波动范围区域

##### generate_response_time_chart()
**功能**: 生成响应时间趋势图
**特点**:
- 按namespace和接口分组
- 每个环境最多显示3个接口
- 使用不同颜色区分接口

#### 2.5 邮件发送模块

##### send_combined_email_report()
**功能**: 发送综合邮件报表
**包含内容**:
- HTML格式报表
- 内嵌图表(base64编码)
- CSV附件(可选)

## 📊 数据结构

### CPU数据结构
```python
{
    'service_name': '服务名',
    'namespace': 'gray/prod',
    'date': '2024-01-01',
    'avg_cpu_usage': 0.3456,
    'max_cpu_usage': 0.5678,
    'min_cpu_usage': 0.1234,
    'data_points': 120
}
```

### 响应时间数据结构
```python
{
    'service_name': '服务名',
    'interface_name': '接口功能(/path)',
    'namespace': 'gray/prod',
    'date': '2024-01-01',
    'avg_response_time_ms': 234.56,
    'max_response_time_ms': 456.78,
    'min_response_time_ms': 123.45,
    'data_points': 120
}
```

## 🎯 监控指标

### CPU监控
- **指标**: `process_cpu_usage`
- **阈值**: 
  - 🔴 高CPU(>0.8)
  - 🟡 中等CPU(>0.5)
  - 🟢 低CPU(≤0.5)

### 响应时间监控
- **指标**: HTTP请求响应时间
- **阈值**:
  - 🔴 慢响应(>1s)
  - 🟡 中等响应(>500ms)
  - 🟢 快响应(≤500ms)

## 🚀 使用方式

### 基本用法
```bash
python3 prometheus_monitor.py \
  --prometheus-url "https://prometheus.example.com" \
  --token "your_token" \
  --days 7
```

### 发送邮件报表
```bash
python3 prometheus_monitor.py \
  --send-email \
  --smtp-user "<EMAIL>" \
  --smtp-password "password" \
  --to-emails "<EMAIL>" \
  --attach-csv
```

### 导出CSV
```bash
python3 prometheus_monitor.py \
  --export-csv \
  --csv-filename "report.csv"
```

## 🔄 执行流程

```mermaid
graph TD
    A[启动脚本] --> B[解析命令行参数]
    B --> C[创建PrometheusMonitor实例]
    C --> D[查询CPU数据 3天/7天]
    D --> E[查询响应时间数据 3天/7天]
    E --> F[生成控制台报表]
    F --> G{需要导出CSV?}
    G -->|是| H[导出CSV文件]
    G -->|否| I{需要发送邮件?}
    H --> I
    I -->|是| J[生成HTML报表]
    J --> K[生成图表]
    K --> L[发送邮件]
    I -->|否| M[结束]
    L --> M
```

## 🎨 可视化特性

### 1. 状态图标
- 🔴 高风险状态
- 🟡 中等风险状态  
- 🟢 正常状态
- ⬇️ 性能改善
- ⬆️ 性能下降

### 2. 颜色编码
- **Gray环境**: 浅灰色背景
- **Prod环境**: 白色背景
- **高CPU/慢响应**: 红色背景
- **中等CPU/响应**: 橙色背景
- **低CPU/快响应**: 绿色背景

### 3. 图表样式
- **Gray环境**: 实线 + 圆点标记
- **Prod环境**: 虚线 + 方块标记
- **填充区域**: 显示数据波动范围

## 🔧 配置说明

### 环境配置
- **namespaces**: ['gray', 'prod'] - 监控的环境
- **监控时间**: 每天9:00-11:00
- **查询步长**: 60秒

### 邮件配置
- **SMTP服务器**: smtp.exmail.qq.com:465
- **支持SSL**: 是
- **附件格式**: CSV

### 图表配置
- **CPU图表**: 12x8英寸
- **响应时间图表**: 14x10英寸
- **图片格式**: PNG (base64编码)

## 🚨 注意事项

1. **时间范围**: 固定为每天9:00-11:00，适合业务高峰期监控
2. **数据聚合**: 按(服务名, namespace, 日期)三维聚合
3. **接口限制**: 响应时间图表每个环境最多显示3个接口
4. **依赖要求**: requests, yagmail, matplotlib
5. **权限要求**: 需要Prometheus查询权限和邮件发送权限

## 📈 扩展建议

1. **添加更多指标**: 内存使用率、磁盘IO等
2. **支持更多环境**: 除gray/prod外的其他环境
3. **告警功能**: 超过阈值时自动告警
4. **历史趋势**: 支持更长时间范围的趋势分析
5. **自定义时间段**: 支持用户自定义监控时间段
