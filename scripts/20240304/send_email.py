import yagmail
from _datetime import datetime,date, timedelta

if __name__ == '__main__':
    # 配置邮箱信息
    email = '<EMAIL>'
    password = 'pPvsf9EXinNgcVG8'
    smtp_server = 'smtp.exmail.qq.com'
    smtp_port = 465  # 使用SSL，端口号465

    # 初始化 yagmail 客户端
    yag = yagmail.SMTP(user=email, password=password, smtp_ssl=True, host='smtp.exmail.qq.com', port=465)
    sendResult = yag.send(to=['<EMAIL>'], subject='尊敬的客户，您好！', contents='您于2024年03月18日消费并开具了电子发票，点击链接查看发票：http://************:57880/saas-industry-test/ebill.html?t=%C3%91_%C3%91Mo%C3%AD%C3%97%C3%85%C4%A8%C4%9F%C4%AFZ2%C3%B9')
    print(sendResult)