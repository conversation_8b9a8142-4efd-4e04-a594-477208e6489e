# -*- coding: utf-8 -*-
"""
追溯码采集日志数据迁移

目标将 v2_goods_stock_traceable_code_log 表中的历史数据迁移到 v3_goods_stock_traceable_code_log 中

1. 判断当前门店是否开启的码上放心（开通了才迁移）
2. 按天查询当前连锁 v2_goods_stock_traceable_code_log 表中的数据 patientOrderId 数据（仅迁移有 patientOrderId 的数据）
3. 查询进销存日志 v2_goods_stock_log 表中 patient_order_id = patientOrderId 的数据（goods 字段需要有 traceableCodeList 字段）
4. 将数据回写到 v3_goods_stock_traceable_code_log 表中

"""
import argparse
import json
import os
import sys
import traceback
from decimal import Decimal

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from idwork import IdWork
from multizone.db import DBClient

from scripts.common.utils.lists import ListUtils
from scripts.common.utils.sqls import SqlUtils
from scripts.common.utils.trace_code_util import TraceCodeUtils


class TraceCodeMigrator:
    def __init__(self, region_name, chain_id, env='prod'):
        self.processed_trace_codes = []
        self.clinic_ids = []
        self.chain_id = chain_id
        self.region_name = region_name
        self.env = env

        # 初始化数据库连接
        self.goods_db_client = DBClient(self.region_name, 'ob', 'abc_cis_goods', self.env, True)
        self.goods_wdb_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', self.env, True)
        self.id_work_client = self.goods_wdb_client

        # 根据环境选择日志数据库
        goods_log_database = 'abc_cis_goods_log'
        if env == 'dev' or env == 'test':
            goods_log_database = 'abc_cis_goods'
        self.goods_log_db_client = DBClient(self.region_name, 'abc_cis_stock_zip', goods_log_database, self.env, True)

        # 用于跟踪已处理的patient_order_id，避免重复处理
        self.processed_trace_code_keys = set()

        print(f"[INFO] 初始化迁移器 - 连锁ID: {chain_id}, 区域: {region_name}, 环境: {env}")

    def check_traceable_code_enabled(self):
        """检查连锁是否开启了码上放心功能"""
        try:
            chain_config = self.goods_db_client.fetchone("""
                select *
                from abc_cis_goods.v2_goods_chain_config
                where JSON_EXTRACT(external_config, '$.aliHealth.status') = 2 and chain_id = '{chain_id}';
            """.format(chain_id=self.chain_id))

            clinic_configs = self.goods_db_client.fetchall("""
                select *
                from abc_cis_goods.v2_goods_clinic_config
                where JSON_EXTRACT(external_config, '$.aliHealth.status') = 2 and chain_id = '{chain_id}';
            """.format(chain_id=self.chain_id))

            if not clinic_configs and not chain_config:
                print(f"[WARNING] 连锁 {self.chain_id} 未找到配置信息")
                return False

            if clinic_configs:
                self.clinic_ids = ListUtils.dist_mapping(clinic_configs, lambda clinic: clinic['clinic_id'])
            else:
                self.clinic_ids = ListUtils.dist_mapping(self.goods_db_client.fetchall("""select id from abc_cis_basic.organ where parent_id = '{chain_id}';""".format(chain_id=self.chain_id)),
                                                         lambda clinic: clinic['id'])

            print(f"[INFO] 连锁 {self.chain_id} {'门店' + SqlUtils.to_in_value(self.clinic_ids) if self.clinic_ids else ''} 码上开通放心")
            return True

        except Exception as e:
            print(f"[ERROR] 检查码上放心功能状态失败: {str(e)}")
            return False

    def load_processed_trace_codes(self):
        """加载已经处理过的patient_order_id列表，避免重复处理"""
        try:
            clinic_ids_str = 'AND clinic_id IN (' + ','.join(f"'{clinic_id}'" for clinic_id in self.clinic_ids) + ')' if self.clinic_ids else ''
            sql = """
                SELECT DISTINCT clinic_id, goods_id, no
                FROM v3_goods_stock_traceable_code_log
                WHERE chain_id = '{chain_id}'
                  {clinic_ids_str}
                  AND patient_order_id IS NOT NULL
                  AND patient_order_id != ''
                order by clinic_id, goods_id, no
            """.format(chain_id=self.chain_id, clinic_ids_str=clinic_ids_str)

            self.processed_trace_codes = self.goods_db_client.fetchall(sql)
            if self.processed_trace_codes:
                print(f"[INFO] 加载了 {len(self.processed_trace_code_keys)} 个已处理的 trace_code")
            else:
                print("[INFO] 没有找到已处理的 trace_code")

        except Exception as e:
            print(f"[ERROR] 加载已处理的 patient_order_id 失败: {str(e)}")
            # 如果加载失败，继续执行，但可能会有重复处理

    def trace_code_key(self, clinic_id, goods_id, no):
        return f"{clinic_id}_{goods_id}_{no}"

    def get_patient_order_ids_by_date(self, date_str):
        """按日期获取v2_goods_stock_traceable_code_log表中有patient_order_id的数据"""
        try:
            sql = """
                SELECT DISTINCT patient_order_id
                FROM v2_goods_stock_traceable_code_log
                WHERE chain_id = '{chain_id}'
                  AND DATE(created) = '{date_str}'
                  AND patient_order_id IS NOT NULL
                  AND patient_order_id != ''
            """.format(chain_id=self.chain_id, date_str=date_str)

            records = self.goods_db_client.fetchall(sql)
            all_patient_order_ids = [record['patient_order_id'] for record in records] if records else []

            # 过滤掉已经处理过的patient_order_id
            new_patient_order_ids = [
                pid for pid in all_patient_order_ids
                if pid not in self.processed_trace_code_keys
            ]

            print(f"[INFO] 日期 {date_str} 找到 {len(all_patient_order_ids)} 个 patient_order_id，其中 {len(new_patient_order_ids)} 个未处理")
            if len(all_patient_order_ids) > len(new_patient_order_ids):
                print(f"[INFO] 跳过了 {len(all_patient_order_ids) - len(new_patient_order_ids)} 个已处理的 patient_order_id")

            return new_patient_order_ids

        except Exception as e:
            print(f"[ERROR] 获取日期 {date_str} 的 patient_order_id 失败: {str(e)}")
            return []

    def get_stock_log_with_traceable_codes(self, patient_order_ids):
        """查询进销存日志中包含traceableCodeList的数据"""
        if not patient_order_ids:
            return []

        try:
            # 构建IN查询条件
            patient_order_ids_str = "','".join(patient_order_ids)

            sql = """
                SELECT id, chain_id, organ_id, goods_id, goods, action,
                       piece_num, piece_count, package_count, package_cost_price,
                       stock_piece_count, stock_package_count, stock_change_cost, stock_total_cost,
                       batch_piece_count, batch_package_count, batch_total_cost,
                       pharmacy_piece_count, pharmacy_package_count, pharmacy_total_cost,
                       goods_piece_count, goods_package_count, goods_total_cost,
                       bat_id, order_id, order_detail_id, batch_id, stock_id,
                       _in_tax_rat, pharmacy_no, supplier_id, created_date, created_user_id,
                       patient_order_id
                FROM v2_goods_stock_log
                WHERE patient_order_id IN ('{patient_order_ids}')
                  AND goods IS NOT NULL
                  AND JSON_VALID(goods) = 1
                  AND JSON_EXTRACT(goods, '$.traceableCodeList') IS NOT NULL
            """.format(patient_order_ids=patient_order_ids_str)

            records = self.goods_log_db_client.fetchall(sql)
            print(f"[INFO] 找到 {len(records)} 条包含 traceableCodeList 的进销存日志")
            return records

        except Exception as e:
            print(f"[ERROR] 查询进销存日志失败: {str(e)}")
            return []

    def extract_traceable_codes_from_goods(self, goods_json_str):
        """从goods字段中提取traceableCodeList"""
        try:
            if not goods_json_str:
                return []

            goods_data = json.loads(goods_json_str)
            traceable_code_list = goods_data.get('traceableCodeList', [])

            if not isinstance(traceable_code_list, list):
                return []

            return traceable_code_list

        except (json.JSONDecodeError, TypeError) as e:
            print(f"[WARNING] 解析goods字段失败: {str(e)}")
            return []

    def migrate_to_v3_table(self, stock_trace_code_logs, max_his_package_count, max_his_total_piece_count, piece_num, insert_v3_records):
        """将数据迁移到v3_goods_stock_traceable_code_log表"""
        left_his_total_piece_count = max_his_total_piece_count
        if not stock_trace_code_logs:
            return 0, left_his_total_piece_count

        migrated_count = 0
        for trace_code_log in stock_trace_code_logs:
            stock_log = trace_code_log['stock_log']
            traceable_code = trace_code_log['trace_code']

            v3_record = self.build_v3_record(stock_log, traceable_code, max_his_package_count)
            if not v3_record:
                continue

            v3_record['before_total_piece_count'] = left_his_total_piece_count
            # change_total_piece_count = abs(v3_record.get('change_piece_count', 0)) - abs(v3_record.get('change_package_count', 0) * piece_num)

            use_change_piece_count = abs(v3_record.get('use_change_piece_count', 0))
            use_change_package_count = abs(v3_record.get('use_change_package_count', 0))
            use_change_total_piece_count = use_change_piece_count + use_change_package_count * piece_num
            change_piece_count = use_change_piece_count
            change_package_count = use_change_package_count
            if v3_record['action'] == 900:
                if use_change_total_piece_count > left_his_total_piece_count:
                    left_his_total_piece_count = 0
                    change_piece_count = left_his_total_piece_count % piece_num
                    change_package_count = left_his_total_piece_count // piece_num
                else:
                    left_his_total_piece_count -= use_change_total_piece_count
            else:
                if use_change_total_piece_count + left_his_total_piece_count > max_his_total_piece_count:
                    real_change_total_piece_count = max_his_total_piece_count - left_his_total_piece_count
                    left_his_total_piece_count = max_his_total_piece_count
                    change_piece_count = real_change_total_piece_count % piece_num
                    change_package_count = real_change_total_piece_count // piece_num
                else:
                    left_his_total_piece_count += use_change_total_piece_count

            if v3_record['action'] == 900:
                v3_record['change_piece_count'] = -change_piece_count
                v3_record['change_package_count'] = -change_package_count
            else:
                v3_record['change_piece_count'] = change_piece_count
                v3_record['change_package_count'] = change_package_count
            v3_record['after_total_piece_count'] = left_his_total_piece_count

            insert_v3_records.append(v3_record)
            migrated_count += 1

        return migrated_count, left_his_total_piece_count

    def build_v3_record(self, stock_log, traceable_code, max_his_package_count):
        """构建v3表的记录数据"""
        try:
            # 解析追溯码数据
            traceable_code_no = traceable_code.get('no', '')
            piece_count = abs(Decimal(traceable_code.get('hisPieceCount', 0)))
            package_count = abs(Decimal(traceable_code.get('hisPackageCount', 0)))
            if piece_count == 0 and package_count == 0:
                return None

            # 映射action字段 - 需要将字符串action转换为对应的数字
            action = stock_log.get('action')
            if action == '发药':
                action_code = 900
                piece_count = -piece_count
                package_count = -package_count
            else:
                action_code = 1000

            v3_record = {
                'id': self.id_work.getUIDLong(),
                'chain_id': stock_log.get('chain_id'),
                'clinic_id': stock_log.get('organ_id'),  # organ_id映射到clinic_id
                'goods_id': stock_log.get('goods_id'),
                'pharmacy_type': 0,  # 默认值
                'pharmacy_no': stock_log.get('pharmacy_no', 0),
                'no': traceable_code_no,  # 追溯码
                'no_type': 0,  # 默认为普通码
                'patient_order_id': stock_log.get('patient_order_id'),
                'midstream_sheet_id': stock_log.get('source_order_id'),
                'midstream_form_item_id': stock_log.get('source_order_detail_id'),
                'downstream_sheet_id': stock_log.get('order_id'),  # 使用order_id作为下游单据id
                'downstream_form_item_id': stock_log.get('order_detail_id'),
                'action': action_code,
                'bat_id': stock_log.get('bat_id'),
                'use_change_piece_count': piece_count,
                'use_change_package_count': package_count,
                'change_piece_count': None,
                'change_package_count': None,
                'after_total_piece_count': None,
                'before_total_piece_count': None,
                'after_max_his_package_count': max_his_package_count,
                'is_deleted': 0,
                'created_by': stock_log.get('created_user_id', ''),
                'created': stock_log.get('created_date'),
                'last_modified_by': stock_log.get('created_user_id', ''),
                'last_modified': stock_log.get('created_date')
            }

            return v3_record

        except Exception as e:
            print(f"[ERROR] 构建v3记录失败: {str(e)}")
            return None

    def insert_v3_record(self, v3_records):
        if not v3_records:
            return

        values_sql = ''
        for i in range(len(v3_records)):
            v3_record = v3_records[i]
            values_sql += f"""({v3_record['id']}, '{v3_record['chain_id']}', '{v3_record['clinic_id']}', '{v3_record['goods_id']}', {v3_record['pharmacy_type']}, {v3_record['pharmacy_no']},
                '{v3_record['no']}', {v3_record['no_type']}, '{v3_record['patient_order_id']}', '{v3_record['midstream_sheet_id']}', '{v3_record['midstream_form_item_id']}',
                '{v3_record['downstream_sheet_id']}', '{v3_record['downstream_form_item_id']}', {v3_record['action']}, '{v3_record['bat_id']}', {v3_record['use_change_piece_count']}, 
                {v3_record['use_change_package_count']}, {v3_record['change_piece_count']}, {v3_record['change_package_count']}, {v3_record['after_total_piece_count']}, 
                {v3_record['before_total_piece_count']}, {v3_record['after_max_his_package_count']}, {v3_record['is_deleted']}, '{v3_record['created_by']}', '{v3_record['created']}', 
                '{v3_record['last_modified_by']}', '{v3_record['last_modified']}')"""
            if i < len(v3_records) - 1:
                values_sql += ',\n'

        """插入记录到v3表"""
        try:
            # 构建插入SQL，按照v3表的字段顺序
            sql = """
                INSERT INTO v3_goods_stock_traceable_code_log (
                    id, chain_id, clinic_id, goods_id, pharmacy_type, pharmacy_no,
                    no, no_type, patient_order_id, midstream_sheet_id, midstream_form_item_id,
                    downstream_sheet_id, downstream_form_item_id, action, bat_id, use_change_piece_count, use_change_package_count, change_piece_count,
                    change_package_count, after_total_piece_count, before_total_piece_count, after_max_his_package_count,
                    is_deleted, created_by, created, last_modified_by, last_modified
                ) VALUES {values_sql};
            """.format(values_sql=values_sql)

            self.goods_wdb_client.execute(sql)

        except Exception as e:
            print(f"[ERROR] 插入v3记录失败: {str(e)}")
            print(f"[DEBUG] 失败的记录: {v3_records}")
            raise

    def migrate_trace_code_log(self, chain_id, clinic_id, start_goods_id, start_no):
        """按照机构迁移数据"""
        # 查找接下来需要迁移的 trace_code
        limit = 100
        total_migrated_trace_code = 0
        total_migrated_trace_code_log = 0
        while True:
            trace_codes = self.query_trace_codes(chain_id, clinic_id, start_goods_id, start_no, limit)
            if not trace_codes:
                break

            total_migrated_trace_code += len(trace_codes)
            start_goods_id, start_no = trace_codes[-1]['goods_id'], trace_codes[-1]['no']

            # 查询追溯码的使用历史
            trace_code_key_to_stock_trace_code_logs = self.query_trace_code_logs(chain_id, clinic_id, trace_codes)
            if not trace_code_key_to_stock_trace_code_logs:
                continue

            # 查询标识符关联信息
            drug_identification_code_to_relations = self.query_trace_code_drug_identification_code(chain_id, trace_codes)
            goods_ids = ListUtils.dist_mapping(trace_codes, lambda e: e['goods_id'])
            goods_id_to_goods = self.query_goods_info(chain_id, goods_ids)

            # 迁移数据
            insert_v3_records = []
            update_trace_codes = []
            for trace_code in trace_codes:
                trace_code_key = self.trace_code_key(trace_code['clinic_id'], trace_code['goods_id'], trace_code['no'])
                stock_trace_code_logs = trace_code_key_to_stock_trace_code_logs.get(trace_code_key, [])
                if not stock_trace_code_logs:
                    continue

                relations = drug_identification_code_to_relations.get(trace_code['drug_identification_code'])
                goods = goods_id_to_goods.get(trace_code['goods_id'])
                if not goods:
                    print(f"[WARNING] 药品信息不存在，跳过迁移: {trace_code}")
                    continue
                max_his_package_count = Decimal(1)
                piece_num = Decimal(goods.get('piece_num', 1))
                max_his_total_piece_count = max_his_package_count * piece_num
                if relations:
                    max_his_total_piece_count = relations[0]['ali_trans_his_piece_count']

                max_his_package_count = max_his_total_piece_count // piece_num

                migrated_log_count, left_his_total_piece_count = self.migrate_to_v3_table(stock_trace_code_logs, max_his_package_count, max_his_total_piece_count, piece_num, insert_v3_records)
                if migrated_log_count > 0:
                    # 如果 migrated_log_count > 0 的话，需要同时修改剩余数量
                    trace_code['piece_count'] = left_his_total_piece_count
                    used = 0
                    if left_his_total_piece_count == 0:
                        used = 1
                    elif max_his_total_piece_count == left_his_total_piece_count:
                        used = 0
                    else:
                        used = 2
                    trace_code['used'] = used
                    update_trace_codes.append(trace_code)

                total_migrated_trace_code_log += migrated_log_count

            if insert_v3_records:
                # 按照 20 一组
                v3_records_groups = [insert_v3_records[i:i + 20] for i in range(0, len(insert_v3_records), 20)]
                for v3_records_group in v3_records_groups:
                    self.insert_v3_record(v3_records_group)

            if update_trace_codes:
                # 按照 20 一组
                trace_code_groups = [update_trace_codes[i:i + 20] for i in range(0, len(update_trace_codes), 20)]
                for trace_code_group in trace_code_groups:
                    self.update_trace_code(clinic_id, trace_code_group)

        return total_migrated_trace_code_log

    def run(self):
        """执行迁移任务"""
        print(f"[INFO] 开始执行追溯码数据迁移任务")
        print(f"[INFO] 连锁ID: {self.chain_id}, 区域: {self.region_name}, 环境: {self.env}")

        try:
            # 1. 检查是否开启了码上放心功能
            if not self.check_traceable_code_enabled():
                print("[INFO] 连锁未开启码上放心功能，跳过迁移")
                return

            self.id_work = IdWork(self.id_work_client, False)
            self.id_work.config()

            # 2. 加载已经处理的 trace_code_no
            print("[INFO] 加载已处理的 clinic_id_goods_id_no 列表...")
            # self.load_processed_trace_codes()

            # 3. 执行迁移
            start_goods_id, start_no = '', ''
            if self.clinic_ids:
                total_migrated = 0
                for clinic_id in self.clinic_ids:
                    clinic_id_to_trace_codes = ListUtils.group_by(self.processed_trace_codes, lambda e: e['clinic_id'])
                    # 按照 clinic_id、goods_id、no 排序，找到起始的 clinic_id、goods_id、no
                    clinic_trace_codes = clinic_id_to_trace_codes.get(clinic_id, [])
                    if clinic_trace_codes:
                        clinic_trace_codes.sort(key=lambda e: (e['clinic_id'], e['goods_id'], e['no']))
                        start_clinic_id, start_goods_id, start_no = clinic_trace_codes[0]['clinic_id'], clinic_trace_codes[0]['goods_id'], clinic_trace_codes[0]['no']
                    clinic_total_migrated = self.migrate_trace_code_log(self.chain_id, clinic_id, start_goods_id, start_no)
                    total_migrated += clinic_total_migrated
                    print(f"[INFO] 门店 {clinic_id} 迁移完成，迁移 {clinic_total_migrated} 条记录")
            else:
                chain_trace_codes = self.processed_trace_codes
                if chain_trace_codes:
                    chain_trace_codes.sort(key=lambda e: (e['clinic_id'], e['goods_id'], e['no']))
                    start_clinic_id, start_goods_id, start_no = chain_trace_codes[0]['clinic_id'], chain_trace_codes[0]['goods_id'], chain_trace_codes[0]['no']
                total_migrated = self.migrate_trace_code_log(self.chain_id, None, start_goods_id, start_no)

            print(f"[SUCCESS] 迁移任务完成！总共迁移 {total_migrated} 条记录")
            print(f"[INFO] 最终处理了 {len(self.processed_trace_code_keys)} 个不重复的 patient_order_id")

        except Exception as e:
            print(f"[ERROR] 迁移任务执行失败: ", e)
            traceback.print_exc()
            raise

    def query_trace_codes(self, chain_id, clinic_id, start_goods_id, start_no, limit):
        clinic_condition = f"AND clinic_id = '{clinic_id}'" if clinic_id else ""
        goods_id_condition = f"AND goods_id >= '{start_goods_id}'" if start_goods_id else ""
        no_condition = f"AND no > '{start_no}'" if start_no else ""

        trace_codes = self.goods_db_client.fetchall("""
            SELECT clinic_id, goods_id, no, group_concat(id) as trace_code_ids
            FROM abc_cis_goods.v2_goods_stock_traceable_code
            WHERE chain_id = '{chain_id}'
              {clinic_condition}
              {goods_id_condition}
              {no_condition}
              AND goods_id != ''
              AND last_modified between '2025-04-27 00:00:00' and current_timestamp
              AND is_deleted = 0
              AND no not in (
                '11111111111111111111',
                '22000000000000000000',
                'WSYM0000000000000000',
                'WZSM0000000000000000',
                '该药品无追溯码',
                '承诺无追溯码',
                'WMYP',
                'WMHC',
                '0',
                '37020020243700000066',
                '37020000000000000000',
                '88888888888888888888',
                '00000000000000000000',
                '00000000',
                '8HB00000000000000000',
                'MTSY_99999',
                'WM000000000000000000'
              )
            group by clinic_id, goods_id, no
            ORDER BY clinic_id, goods_id, no
            LIMIT {limit}
        """.format(chain_id=chain_id, clinic_condition=clinic_condition, goods_id_condition=goods_id_condition, no_condition=no_condition, limit=limit))

        return trace_codes

    def query_trace_code_logs(self, chain_id, clinic_id, trace_codes):
        if not trace_codes:
            return []

        # 1. 先查询进销存日志
        clinic_condition = f"AND organ_id = '{clinic_id}'" if clinic_id else ""
        trace_code_conditions = "AND (" + " OR ".join([f"(goods_id = '{trace_code['goods_id']}' AND goods like '%{trace_code['no']}%')" for trace_code in trace_codes]) + ")"
        sql = """
            SELECT *
            FROM abc_cis_goods_log.v2_goods_stock_log
            WHERE chain_id = '{chain_id}'
              {clinic_condition}
              {trace_code_conditions}
              AND created_date between '2025-04-27 00:00:00' and current_timestamp
              AND action in ('发药', '退药')
            ORDER BY created_date
        """.format(chain_id=chain_id, clinic_condition=clinic_condition, trace_code_conditions=trace_code_conditions)
        stock_logs = self.goods_log_db_client.fetchall(sql)
        if not stock_logs:
            return []

        trace_code_keys = set()
        for trace_code in trace_codes:
            trace_code_keys.add(self.trace_code_key(trace_code['clinic_id'], trace_code['goods_id'], trace_code['no']))

        # 2. 对数据进行加工，因为 goods 里面存储的追溯码是数组，需要展开，然后过滤掉无用数据
        key_to_stock_trace_code_logs = {}
        for stock_log in stock_logs:
            if not stock_log.get('goods') or not isinstance(stock_log['goods'], str):
                continue

            try:
                goods_data = json.loads(stock_log['goods'])
                if not goods_data or not isinstance(goods_data, dict):
                    continue
                traceable_code_list = goods_data.get('traceableCodeList', [])
                if not traceable_code_list or not isinstance(traceable_code_list, list):
                    continue
                for traceable_code in traceable_code_list:
                    if not traceable_code or not isinstance(traceable_code, dict):
                        continue

                    # 如果没有 hisPieceCount 和 hisPackageCount 字段，说明是旧数据，需要跳过
                    if 'hisPieceCount' not in traceable_code and 'hisPackageCount' not in traceable_code:
                        continue

                    trace_code_key = self.trace_code_key(stock_log['organ_id'], stock_log['goods_id'], traceable_code.get('no', ''))
                    if trace_code_key not in trace_code_keys:
                        continue

                    if trace_code_key not in key_to_stock_trace_code_logs:
                        key_to_stock_trace_code_logs[trace_code_key] = []

                    key_to_stock_trace_code_logs[trace_code_key].append({
                        'trace_code': traceable_code,
                        'stock_log': stock_log
                    })
            except Exception as e:
                print(f"[WARNING] 解析goods字段失败: {str(e)}")
                continue

        return key_to_stock_trace_code_logs

    def query_trace_code_drug_identification_code(self, chain_id, trace_codes):
        if not trace_codes:
            return {}

        drug_identification_code_keys = set()
        for trace_code in trace_codes:
            drug_identification_code = TraceCodeUtils.drug_identification_code(trace_code['no'])
            if not drug_identification_code:
                continue

            trace_code['drug_identification_code'] = drug_identification_code.drug_identification_code
            drug_identification_code_keys.add((
                trace_code['goods_id'],
                trace_code['drug_identification_code']
            ))

        if not drug_identification_code_keys:
            return {}

        # drug_identification_code_conditions = " AND (" + " OR ".join([f"drug_identification_code = '{drug_identification_code}'" for drug_identification_code in drug_identification_code_keys]) + ")"
        drug_identification_code_conditions = " AND (" + " OR ".join(
            [f"(goods_id = '{goods_id}' AND drug_identification_code = '{drug_identification_code}')" for goods_id, drug_identification_code in drug_identification_code_keys]) + ")"
        sql = """
            SELECT id, goods_id, drug_identification_code, ali_trans_his_piece_count
            FROM abc_cis_goods.v2_goods_identification_code_relation
            WHERE chain_id = '{chain_id}'
              {drug_identification_code_conditions}
            AND type = 0 AND is_deleted = 0 AND ali_trans_his_piece_count is not null
        """.format(chain_id=chain_id, drug_identification_code_conditions=drug_identification_code_conditions)

        drug_identification_code_to_relations = ListUtils.group_by(self.goods_db_client.fetchall(sql), lambda e: e['drug_identification_code'])

        return drug_identification_code_to_relations

    def query_goods_info(self, chain_id, goods_ids):
        if not goods_ids:
            return {}

        goods_list = self.goods_db_client.fetchall("""
            SELECT id, piece_num
            FROM abc_cis_goods.v2_goods
            WHERE organ_id = '{chain_id}'
              AND id IN ({goods_ids})
        """.format(chain_id=chain_id, goods_ids=SqlUtils.to_in_value(goods_ids)))

        return ListUtils.to_map(goods_list, lambda e: e['id'])

    def update_trace_code(self, clinic_id, trace_codes):
        """
        修改追溯码的剩余数量，和 flag 位为 1
        :param clinic_id: 门店ID
        :param trace_codes: 需要修改的追溯码列表
        :return:
        """

        ids = []
        piece_count_case_when_statement_sql = ""
        used_case_when_statement_sql = ""
        for trace_code in trace_codes:
            trace_code_ids = trace_code['trace_code_ids'].split(',')
            ids.extend(trace_code_ids)
            for trace_code_id in trace_code_ids:
                piece_count_case_when_statement_sql += f"WHEN id = '{trace_code_id}' THEN {trace_code['piece_count']} "
                used_case_when_statement_sql += f"WHEN id = '{trace_code_id}' THEN {trace_code['used']} "

        sql = """
            UPDATE abc_cis_goods.v2_goods_stock_traceable_code
            SET piece_count = CASE {piece_count_case_when_statement_sql} ELSE 0 END, used = CASE {used_case_when_statement_sql} ELSE 0 END, flag = 1
            WHERE id IN ({trace_code_ids})
        """.format(piece_count_case_when_statement_sql=piece_count_case_when_statement_sql,
                   used_case_when_statement_sql=used_case_when_statement_sql,
                   trace_code_ids=SqlUtils.to_in_value(ids))

        self.goods_wdb_client.execute(sql)


def main():
    parser = argparse.ArgumentParser(description='追溯码采集日志数据迁移工具')
    parser.add_argument('--chain-id', required=True, help='连锁ID')
    parser.add_argument('--region-name', required=True, help='分区名称（如：ShangHai, HangZhou）')
    parser.add_argument('--env', default='prod', help='环境（prod/dev/test）')

    args = parser.parse_args()

    if not args.chain_id or not args.region_name:
        parser.print_help()
        sys.exit(-1)

    try:
        # 创建迁移器实例
        migrator = TraceCodeMigrator(args.region_name, args.chain_id, args.env)

        # 执行实际迁移
        migrator.run()

    except Exception as e:
        print(f"[ERROR] 程序执行失败: ", e)
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
