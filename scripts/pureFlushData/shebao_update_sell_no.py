#! /usr/bin/env python3
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
import time
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import argparse
import logging


from multizone.db import DBClient

# 清理档案删除对码信息还存在的信息
def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--env', help='环境dev/test/prod')
    parser.add_argument('--regionName', help='ShangHai/HangZhou')
    parser.add_argument('--beginDate', help='开始日期')
    parser.add_argument('--endDate', help='结束日期')
    args = parser.parse_args()
    # args.env = 'dev'
    # args.regionName = 'ShangHai'
    # args.beginDate = '2025-01-01'
    # args.endDate = '2025-07-01'
    if not args.regionName or not args.env or not args.beginDate or not args.endDate:
        parser.print_help()
        sys.exit(-1)

    shebao_ods_client = DBClient(args.regionName, 'abc_cis_bill', 'abc_cis_shebao', args.env, True)
    shebao_ob_client = DBClient(args.regionName, 'ob', 'abc_cis_shebao', args.env, True)


    abc_cis_shebao = 'abc_cis_shebao'
    abc_cis_basic = 'abc_cis_basic'
    abc_cis_charge = 'abc_cis_charge'
    abc_cis_patientOrder = 'abc_cis_patientOrder'
    abc_cis_patient = 'abc_cis_patient'
    if args.env != 'prod':
        abc_cis_shebao = abc_cis_shebao + '_' + args.env
        abc_cis_basic = abc_cis_basic + '_' + args.env
        abc_cis_charge = abc_cis_charge + '_' + args.env
        abc_cis_patientOrder = abc_cis_patientOrder + '_' + args.env
        abc_cis_patient = abc_cis_patient + '_' + args.env

    beginTime = args.beginDate + ' 00:00:00'
    endTime = args.endDate + ' 23:59:59'
    # 查询clinicId
    # clinic_id_sql = f"""
    #     select clinic_id from {abc_cis_basic}.organ a inner join {abc_cis_shebao}.shebao_clinic_config b on a.id = b.clinic_id and a.his_type = 10 and b.status = 10
    #     """
    clinic_id_sql = f"""
        select clinic_id from {abc_cis_shebao}.shebao_clinic_config b where  status = 10;
        """
    clinic_id_list = shebao_ob_client.fetchall(clinic_id_sql)
    if len(clinic_id_list) == 0:
        print('clinic_id_list is empty')
        return

    # 循环clinicId
    for clinic_id in clinic_id_list:
        clinic_id = clinic_id['clinic_id']
        logging.info(
            'start------> clinic_id：{}， env：{}， regionName：{}'.format(clinic_id, args.env, args.regionName))
        # sqlUpdateSelect = f"""
        #      select concat("update shebao_national_payment_result set sell_no = '", c.sell_no, "' where id = '", b.id, "';") as update_sql
        #         from {abc_cis_shebao}.shebao_pay_task a
        #             inner join {abc_cis_shebao}.shebao_national_payment_result b on a.payment_result_id = b.id
        #             inner join {abc_cis_charge}.v2_charge_sheet c on c.id = a.charge_sheet_id and c.clinic_id = b.clinic_id
        #      where a.clinic_id = '{clinic_id}' and a.charged_time between '{beginTime}' and '{endTime}';
        #     """
        # print('sqlUpdateSelect:', sqlUpdateSelect)
        # update_sql_res = shebao_ob_client.fetchall(sqlUpdateSelect)
        # for update_sql in update_sql_res:
        #     update_sql = update_sql['update_sql']
        #     print('update_sql:', update_sql)
        #     shebao_ods_client.execute(update_sql)

        # 重新构建SQL查询，修复路径表达式问题
        sqlUpdateSelectPatientOrder = f"""
            select concat(
                "update shebao_pay_task set patient_id = '", d.patient_id, "', extend_info = ",
                CASE
                    WHEN extend_info IS NULL THEN
                        concat(
                            "JSON_OBJECT('patientId', '", d.patient_id, "', 'patientName', '", d.patient_name, "'",
                            IF(d.patient_mobile IS NOT NULL AND d.patient_mobile != '', concat(", 'mobile', '", d.patient_mobile, "'"), ""),
                            IF(
                                ((p.address_province_id IS NOT NULL AND p.address_province_id != '') OR 
                                 (p.address_city_id IS NOT NULL AND p.address_city_id != '') OR 
                                 (p.address_district_id IS NOT NULL AND p.address_district_id != '') OR 
                                 (p.address_detail IS NOT NULL AND p.address_detail != '')),
                                concat(
                                    ", 'address', JSON_OBJECT(",
                                    CONCAT_WS(", ",
                                        IF(p.address_province_id IS NOT NULL AND p.address_province_id != '', concat("'addressProvinceId', '", p.address_province_id, "'"), NULL),
                                        IF(p.address_province_name IS NOT NULL AND p.address_province_name != '', concat("'addressProvinceName', '", p.address_province_name, "'"), NULL),
                                        IF(p.address_city_id IS NOT NULL AND p.address_city_id != '', concat("'addressCityId', '", p.address_city_id, "'"), NULL),
                                        IF(p.address_city_name IS NOT NULL AND p.address_city_name != '', concat("'addressCityName', '", p.address_city_name, "'"), NULL),
                                        IF(p.address_district_id IS NOT NULL AND p.address_district_id != '', concat("'addressDistrictId', '", p.address_district_id, "'"), NULL),
                                        IF(p.address_district_name IS NOT NULL AND p.address_district_name != '', concat("'addressDistrictName', '", p.address_district_name, "'"), NULL),
                                        IF(p.address_detail IS NOT NULL AND p.address_detail != '', concat("'addressDetail', '", p.address_detail, "'"), NULL),
                                        IF(p.address_geo IS NOT NULL AND p.address_geo != '', concat("'addressGeo', '", p.address_geo, "'"), NULL)
                                    ),
                                    ")"
                                ),
                                ""
                            ),
                            ")"
                        )
                    ELSE
                        concat(
                            "JSON_MERGE_PATCH(extend_info, JSON_OBJECT(",
                            CONCAT_WS(", ",
                                IF(d.patient_mobile IS NOT NULL AND d.patient_mobile != '', concat("'mobile', '", d.patient_mobile, "'"), NULL),
                                IF(
                                    ((p.address_province_id IS NOT NULL AND p.address_province_id != '') OR 
                                     (p.address_city_id IS NOT NULL AND p.address_city_id != '') OR 
                                     (p.address_district_id IS NOT NULL AND p.address_district_id != '') OR 
                                     (p.address_detail IS NOT NULL AND p.address_detail != '')),
                                    concat("'address', JSON_OBJECT(",
                                    CONCAT_WS(", ",
                                        IF(p.address_province_id IS NOT NULL AND p.address_province_id != '', concat("'addressProvinceId', '", p.address_province_id, "'"), NULL),
                                        IF(p.address_province_name IS NOT NULL AND p.address_province_name != '', concat("'addressProvinceName', '", p.address_province_name, "'"), NULL),
                                        IF(p.address_city_id IS NOT NULL AND p.address_city_id != '', concat("'addressCityId', '", p.address_city_id, "'"), NULL),
                                        IF(p.address_city_name IS NOT NULL AND p.address_city_name != '', concat("'addressCityName', '", p.address_city_name, "'"), NULL),
                                        IF(p.address_district_id IS NOT NULL AND p.address_district_id != '', concat("'addressDistrictId', '", p.address_district_id, "'"), NULL),
                                        IF(p.address_district_name IS NOT NULL AND p.address_district_name != '', concat("'addressDistrictName', '", p.address_district_name, "'"), NULL),
                                        IF(p.address_detail IS NOT NULL AND p.address_detail != '', concat("'addressDetail', '", p.address_detail, "'"), NULL),
                                        IF(p.address_geo IS NOT NULL AND p.address_geo != '', concat("'addressGeo', '", p.address_geo, "'"), NULL)
                                    ),
                                    ")")
                                , NULL)
                            ),
                            "))"
                        )
                END,
                " where id = '", a.id, "';"
            ) as update_sql
            from {abc_cis_shebao}.shebao_pay_task a
            inner join {abc_cis_patientOrder}.v2_patientorder d on a.patient_order_id = d.id and a.clinic_id = d.clinic_id
            left join {abc_cis_patient}.v2_patient p on d.patient_id = p.id
            where a.clinic_id = '{clinic_id}' and a.charged_time between '{beginTime}' and '{endTime}';
        """
        print('sqlUpdateSelectPatientOrder:', sqlUpdateSelectPatientOrder)
        update_sql_res = shebao_ob_client.fetchall(sqlUpdateSelectPatientOrder)
        
        # 添加计数器和等待逻辑
        counter = 0
        total_count = len(update_sql_res)
        logging.info(f'共需执行 {total_count} 条SQL语句')
        
        for update_sql in update_sql_res:
            update_sql = update_sql['update_sql']
            print('update_sql:', update_sql)
            shebao_ods_client.execute(update_sql)
            
            # 增加计数器
            counter += 1
            
            # 每1000条等待5秒
            if counter % 1000 == 0:
                logging.info(f'已处理 {counter}/{total_count} 条SQL语句。等待5秒...')
                time.sleep(5)  # 暂停5秒

        logging.info(
            'finish------> clinic_id：{}， env：{}， regionName：{}'.format(clinic_id, args.env, args.regionName))
if __name__ == '__main__':
    main()
