#! /usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Prometheus业务监控报表
获取最近2天每天9点到11点的process_cpu_usage平均值
输出格式：服务名，日期，cpu平均值
支持邮件发送功能
"""

import argparse
import logging
import os
import sys
import tempfile
from datetime import datetime, timedelta
from io import BytesIO
from base64 import b64encode

import requests
import yagmail
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from collections import defaultdict

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 核心接口列表：[服务名, 接口功能, 接口路径]
core_interfaces = [
    ["abc-cis-sc-clinic-service", "批量查询employee", "/rpc/v3/clinics/employees/query-by-ids", "POST"],
    ["abc-cis-sc-clinic-service", "条件批量查询employee", "/api/v3/clinics/employees/list-by-condition", "POST"],
    ["abc-cis-sc-clinic-service", "批量查询门店信息", "/rpc/v3/clinics/departments/query-by-ids", "POST"],
    ["abc-cis-sc-clinic-service", "批量查询科室信息", "/rpc/v3/clinics/organs/list-by-ids", "POST"],
    ["abc-cis-sc-clinic-service", "登陆人信息", "/api/v3/clinics/employees/me", "GET"],
    ["abc-cis-registration-service", "挂号", "/api/v2/registrations/manage", "POST"],
    ["abc-cis-registration-service", "挂号QL", " /api/v2/registrations/itemlist", "GET"],
    ["abc-cis-registration-service", "查询挂号单", " /api/v2/registrations/{registrationSheetId}", "GET"],
    ["abc-cis-registration-service", "rpc拉取挂号单", "/rpc/registrations/patientorders/{patientOrderId}", "GET"],
    ["abc-cis-outpatient-service", "门诊QL", "/api/v2/outpatients", "GET"],
    ["abc-cis-outpatient-service", "查询门诊单", "/api/v2/outpatients/{id}/", "GET"],
    ["abc-cis-outpatient-service", "提交门诊单", "/api/v2/outpatients/{id}/", "PUT"],
    ["abc-cis-charge-service", "收费QL", "/api/v2/charges", "GET"],
    ["abc-cis-charge-service", "查询收费单", "/api/v2/charges/{id}", "GET"],
    ["abc-cis-charge-service", "提交收费", "/api/v2/charges/{id}/paid", "PUT"],
    ["abc-cis-charge-service", "算费", "/api/v2/charges/calculate", "POST"],
    ["abc-cis-patient-order-service", "查询就诊单", "/rpc/patientorders/{id}", "GET"],
    ["abc-cis-patient-order-service", "批量查询就诊单", "/rpc/patientorders/list", "POST"],
    ["abc-cis-patient-order-service", "创建就诊单", "/rpc/patientorders/", "POST"],
    ["abc-cis-sc-goods-service", "rpc查询goods", "/rpc/v3/goods/find-goods-by-pharmacy-v2", "POST"],
    ["abc-cis-sc-goods-service", "goods搜索", "/api/v3/goods/search", "POST"],
    ["abc-cis-sc-goods-service", "rpc查询goods-根据药房", "/rpc/v3/goods/find-goods-by-pharmacy", "POST"],
    ["abc-cis-sc-goods-service", "rpc查询goods-根据药房v2", "/rpc/v3/goods/find-goods-by-pharmacy-v2", "POST"],
    ["abc-cis-crm-service", "患者搜索", "/api/v2/crm/patients/query", "GET"],
    ["abc-cis-crm-service", "查询患者", "/api/v2/crm/patients/{patientId}", "GET"],
    ["abc-cis-crm-service", "rpc查询患者", "/rpc/crm/patients/basic/{patientId}", "GET"],
    # ["abc-cis-crm-service", "rpc查询患者详情", "/rpc/crm/patients/{patientId}", "GET"],
    ["abc-cis-crm-service", "rpc批量查询患者", "/rpc/crm/patients", "POST"],
    ["abc-cis-crm-service", "rpc创建更新患者", "/rpc/crm/patients/basic", "POST"],
    ["abc-cis-crm-service", "rpc轻量级查询患者信息", "/rpc/crm/patients/mini/basic/list", "POST"],
    ["abc-cis-examination-service", "检查检验QL", "/api/v2/examinations", "GET"],
    ["abc-cis-examination-service", "查询检查检验单", "api/v2/examinations/{examSheetId}", "GET"],
    ["abc-cis-shebao-service", "医保收费", "/api/v2/shebao/national/charge/tasks/{id}/paid", "PUT"],
    ["abc-cis-shebao-service", "查询医保收费单", "/api/v2/shebao/national/charge/tasks/{id}", "GET"],
    ["abc-cis-shebao-service", "医保对码", "/api/v2/shebao/code/get_matched_codes", "POST"],
    # 可以根据需要添加更多核心接口
]


class PrometheusMonitor:
    def __init__(self, prometheus_url, token):
        self.prometheus_url = prometheus_url.rstrip('/')
        self.token = token

    def query_prometheus(self, query, start_time, end_time, step='60s'):
        """
        查询Prometheus数据
        """
        url = "{}/api/v1/query_range".format(self.prometheus_url)
        params = {
            'query': query,
            'start': start_time,
            'end': end_time,
            'step': step
        }

        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': self.token
        }

        try:
            response = requests.get(url, params=params, headers=headers, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error("查询Prometheus失败: {}".format(e))
            return None

    def get_cpu_usage_report(self, days=2):
        """
        获取最近指定天数每天9-11点的CPU使用率平均值、最大值和最小值
        按服务名、namespace和日期维度聚合数据
        """
        # 用于存储所有原始数据的字典，key为(service_name, namespace, date)
        raw_data = {}

        # 计算日期范围
        today = datetime.now().date()

        # 定义要查询的namespace
        namespaces = ['gray', 'prod']

        for i in range(days):
            target_date = today - timedelta(days=i)

            # 设置时间范围：9:00-11:00
            start_datetime = datetime.combine(target_date, datetime.min.time().replace(hour=9))
            end_datetime = datetime.combine(target_date, datetime.min.time().replace(hour=11))

            # 转换为Unix时间戳
            start_timestamp = int(start_datetime.timestamp())
            end_timestamp = int(end_datetime.timestamp())

            logger.info(f"查询日期: {target_date}, 时间范围: {start_datetime} - {end_datetime}")

            # 分别查询每个namespace的数据
            for namespace in namespaces:
                # 查询CPU使用率数据，增加namespace过滤
                query = f'process_cpu_usage{{namespace="{namespace}"}}'
                data = self.query_prometheus(query, start_timestamp, end_timestamp, '60s')

                if not data or data.get('status') != 'success':
                    logger.warning(f"查询 {target_date} namespace={namespace} 数据失败")
                    continue

                # 处理查询结果，收集所有原始数据
                for result in data['data']['result']:
                    metric = result['metric']
                    values = result['values']

                    # 获取服务名和namespace
                    service_name = metric.get('application') or metric.get('job', 'unknown')
                    ns = metric.get('namespace', namespace)  # 使用查询的namespace作为默认值
                    date_str = target_date.strftime('%Y-%m-%d')

                    if not values:
                        logger.warning(f"服务 {service_name} namespace={ns} 在 {target_date} 无数据")
                        continue

                    # 提取有效的CPU值
                    cpu_values = [float(value[1]) for value in values if value[1] != 'NaN']

                    if cpu_values:
                        # 使用(服务名, namespace, 日期)作为key来聚合数据
                        key = (service_name, ns, date_str)
                        if key not in raw_data:
                            raw_data[key] = []
                        raw_data[key].extend(cpu_values)

        # 对聚合后的数据进行统计计算
        results = []
        for (service_name, namespace, date_str), all_cpu_values in raw_data.items():
            if all_cpu_values:
                avg_cpu = sum(all_cpu_values) / len(all_cpu_values)
                max_cpu = max(all_cpu_values)
                min_cpu = min(all_cpu_values)

                results.append({
                    'service_name': service_name,
                    'namespace': namespace,
                    'date': date_str,
                    'avg_cpu_usage': round(avg_cpu, 6),
                    'max_cpu_usage': round(max_cpu, 6),
                    'min_cpu_usage': round(min_cpu, 6),
                    'data_points': len(all_cpu_values)  # 添加数据点数量用于调试
                })
                logger.info(f"服务: {service_name}, namespace: {namespace}, 日期: {date_str}, 数据点: {len(all_cpu_values)}, CPU平均值: {avg_cpu:.6f}, 最大值: {max_cpu:.6f}, 最小值: {min_cpu:.6f}")
            else:
                logger.warning(f"服务 {service_name} namespace={namespace} 在 {date_str} 无有效CPU数据")

        return results

    def print_report(self, results):
        """
        打印报表结果
        """
        if not results:
            print("\n❌ 没有找到任何数据")
            return

        print("\n" + "=" * 80)
        print("📊 Prometheus CPU使用率监控报表")
        print("=" * 80)
        print(f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

        # 计算最大服务名长度以动态调整列宽
        max_service_name_len = max(len(result['service_name']) for result in results)
        service_col_width = max(20, min(max_service_name_len + 2, 50))

        # 打印表头
        header = f"{'服务名':<{service_col_width}} {'日期':<12} {'CPU平均值':<12} {'CPU最大值':<12} {'CPU最小值':<12} {'数据点':<8}"
        print(header)
        print("-" * len(header))

        # 按服务名和日期排序
        sorted_results = sorted(results, key=lambda x: (x['service_name'], x['date']))

        # 按服务分组显示
        current_service = None
        for result in sorted_results:
            # 如果是新的服务，添加分隔线
            if current_service != result['service_name']:
                if current_service is not None:
                    print("-" * len(header))
                current_service = result['service_name']

            # 格式化CPU值显示
            avg_cpu_str = f"{result['avg_cpu_usage']:.4f}"
            max_cpu_str = f"{result['max_cpu_usage']:.4f}"
            min_cpu_str = f"{result['min_cpu_usage']:.4f}"
            data_points = result.get('data_points', 0)

            # 添加颜色标识（使用emoji）
            if result['avg_cpu_usage'] > 0.8:
                status_icon = "🔴"  # 高CPU使用率
            elif result['avg_cpu_usage'] > 0.5:
                status_icon = "🟡"  # 中等CPU使用率
            else:
                status_icon = "🟢"  # 低CPU使用率

            service_display = f"{status_icon} {result['service_name']}"

            print(f"{service_display:<{service_col_width+2}} {result['date']:<12} {avg_cpu_str:<12} {max_cpu_str:<12} {min_cpu_str:<12} {data_points:<8}")

        print("=" * len(header))
        print(f"📈 总计: {len(results)} 条记录")
        print(f"🔴 高CPU(>0.8)  🟡 中等CPU(>0.5)  🟢 低CPU(≤0.5)")
        print("=" * 80)

    def export_to_csv(self, results, filename=None):
        """
        导出结果到CSV文件
        """
        if not filename:
            filename = f"prometheus_cpu_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

        try:
            import csv
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['service_name', 'date', 'avg_cpu_usage']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                # 按服务名和日期排序
                sorted_results = sorted(results, key=lambda x: (x['service_name'], x['date']))
                for result in sorted_results:
                    writer.writerow(result)

            logger.info(f"报表已导出到: {filename}")
            return filename
        except Exception as e:
            logger.error(f"导出CSV失败: {e}")
            return None

    def generate_critical_alerts_report(self, cpu_results_table, response_time_results_table, threshold=15.0):
        """
        生成重点关注报表，筛选变化幅度超过指定阈值的监控数据
        """
        critical_alerts = []

        if not cpu_results_table and not response_time_results_table:
            return critical_alerts

        # 获取所有日期
        all_dates = []
        if cpu_results_table:
            all_dates.extend(result['date'] for result in cpu_results_table)
        if response_time_results_table:
            all_dates.extend(result['date'] for result in response_time_results_table)
        all_dates = sorted(set(all_dates))

        if len(all_dates) < 2:
            return critical_alerts

        recent_dates = sorted(all_dates)[-2:]  # 最近2天

        # 分析CPU数据的变化幅度
        if cpu_results_table:
            cpu_by_service_ns = {}
            for result in cpu_results_table:
                key = (result['service_name'], result['namespace'])
                if key not in cpu_by_service_ns:
                    cpu_by_service_ns[key] = {'service_name': result['service_name'], 'namespace': result['namespace'], 'dates': {}}
                cpu_by_service_ns[key]['dates'][result['date']] = result

            for (service_name, namespace), service_data in cpu_by_service_ns.items():
                if recent_dates[0] in service_data['dates'] and recent_dates[1] in service_data['dates']:
                    first_value = service_data['dates'][recent_dates[0]]['avg_cpu_usage']
                    second_value = service_data['dates'][recent_dates[1]]['avg_cpu_usage']

                    if first_value > 0:
                        change_rate = (second_value - first_value) / first_value * 100
                        if change_rate >= threshold:
                            critical_alerts.append({
                                'type': 'CPU使用率',
                                'service_name': service_name,
                                'namespace': namespace,
                                'interface_name': '-',
                                'first_date': recent_dates[0],
                                'second_date': recent_dates[1],
                                'first_value': first_value,
                                'second_value': second_value,
                                'change_rate': change_rate,
                                'unit': '',
                                'severity': 'high' if abs(change_rate) >= 50 else 'medium'
                            })

        # 分析响应时间数据的变化幅度
        if response_time_results_table:
            rt_by_interface_ns = {}
            for result in response_time_results_table:
                key = (result['service_name'], result['interface_name'], result['namespace'])
                if key not in rt_by_interface_ns:
                    rt_by_interface_ns[key] = {
                        'service_name': result['service_name'],
                        'interface_name': result['interface_name'],
                        'namespace': result['namespace'],
                        'dates': {}
                    }
                rt_by_interface_ns[key]['dates'][result['date']] = result

            for (service_name, interface_name, namespace), interface_data in rt_by_interface_ns.items():
                if recent_dates[0] in interface_data['dates'] and recent_dates[1] in interface_data['dates']:
                    first_value = interface_data['dates'][recent_dates[0]]['avg_response_time_ms']
                    second_value = interface_data['dates'][recent_dates[1]]['avg_response_time_ms']

                    if first_value > 0:
                        change_rate = (second_value - first_value) / first_value * 100
                        if abs(change_rate) >= threshold:
                            critical_alerts.append({
                                'type': '响应时间',
                                'service_name': service_name,
                                'namespace': namespace,
                                'interface_name': interface_name,
                                'first_date': recent_dates[0],
                                'second_date': recent_dates[1],
                                'first_value': first_value,
                                'second_value': second_value,
                                'change_rate': change_rate,
                                'unit': 'ms',
                                'severity': 'high' if abs(change_rate) >= 50 else 'medium'
                            })

        # 按变化幅度排序，绝对值大的排在前面
        critical_alerts.sort(key=lambda x: abs(x['change_rate']), reverse=True)

        return critical_alerts

    def generate_combined_html_report(self, cpu_results_table, response_time_results_table, cpu_results_chart,
                                      response_time_results_chart):
        """
        生成包含CPU和响应时间的综合HTML报表，支持按namespace区分环境
        """
        if not cpu_results_table and not response_time_results_table:
            return "<p>没有找到任何数据</p>"

        # 获取所有日期（使用表格数据）
        all_dates = sorted(set(result['date'] for result in cpu_results_table)) if cpu_results_table else []

        # 按服务名和namespace组织CPU数据（表格用）
        cpu_by_service_ns = {}
        if cpu_results_table:
            for result in cpu_results_table:
                service_name = result['service_name']
                namespace = result['namespace']
                key = (service_name, namespace)
                if key not in cpu_by_service_ns:
                    cpu_by_service_ns[key] = {'service_name': service_name, 'namespace': namespace, 'dates': {}}
                cpu_by_service_ns[key]['dates'][result['date']] = result

        # 处理响应时间数据（表格用）
        response_time_by_interface_ns = {}
        if response_time_results_table:
            if cpu_results_table:
                all_dates = sorted(set(date for dates in [set(result['date'] for result in cpu_results_table),
                                                          set(result['date'] for result in response_time_results_table)]
                                       for date in dates))
            else:
                all_dates = sorted(set(result['date'] for result in response_time_results_table))

            for result in response_time_results_table:
                # 使用(服务名, 接口名, namespace)作为键
                key = (result['service_name'], result['interface_name'], result['namespace'])
                if key not in response_time_by_interface_ns:
                    response_time_by_interface_ns[key] = {
                        'service_name': result['service_name'],
                        'interface_name': result['interface_name'],
                        'namespace': result['namespace'],
                        'dates': {}
                    }
                # 存储每个日期的响应时间
                response_time_by_interface_ns[key]['dates'][result['date']] = result

        # 生成CSS样式
        css_styles = (
            "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background-color: #f5f5f5; } "
            ".container { max-width: 1400px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); } "
            "h1 { color: #333; text-align: center; margin-bottom: 10px; } "
            "h2 { color: #555; margin-top: 30px; margin-bottom: 15px; border-bottom: 2px solid #4CAF50; padding-bottom: 5px; } "
            ".report-info { text-align: center; color: #666; margin-bottom: 20px; } "
            "table { border-collapse: collapse; width: 100%; margin-top: 20px; } "
            "th, td { border: 1px solid #ddd; padding: 8px 6px; text-align: center; font-size: 12px; } "
            "th { background-color: #4CAF50; color: white; font-weight: bold; } "
            "tr:nth-child(even) { background-color: #f9f9f9; } "
            "tr:hover { background-color: #f5f5f5; } "
            ".cpu-high { background-color: #ffebee; color: #d32f2f; font-weight: bold; } "
            ".cpu-medium { background-color: #fff3e0; color: #f57c00; font-weight: bold; } "
            ".cpu-low { background-color: #e8f5e8; color: #388e3c; } "
            ".rt-slow { background-color: #ffebee; color: #d32f2f; font-weight: bold; } "
            ".rt-medium { background-color: #fff3e0; color: #f57c00; font-weight: bold; } "
            ".rt-fast { background-color: #e8f5e8; color: #388e3c; } "
            ".rt-improved { background-color: #e8f5e8; color: #388e3c; font-weight: bold; } "
            ".rt-worse { background-color: #ffebee; color: #d32f2f; font-weight: bold; } "
            ".service-name { text-align: left; font-weight: bold; min-width: 150px; } "
            ".namespace { text-align: center; font-weight: bold; min-width: 80px; } "
            ".interface-name { text-align: left; min-width: 200px; } "
            ".cpu-avg { font-size: 14px; font-weight: bold; } "
            ".cpu-minmax { font-size: 11px; color: #666; } "
            ".namespace-gray { background-color: #f0f0f0; } "
            ".namespace-prod { background-color: #fff; } "
            ".chart-section { margin: 30px 0; } "
            ".chart-container { margin-bottom: 30px; overflow: hidden; } "
            ".chart-item { display: inline-block; width: 48%; min-width: 400px; text-align: center; vertical-align: top; margin: 1%; } "
            ".chart-item img { width: 100%; max-width: 600px; height: auto; border: 1px solid #ddd; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); } "
            "h3 { color: #444; margin-top: 25px; margin-bottom: 15px; font-size: 16px; } "
            ".legend { margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; font-size: 11px; color: #666; } "
            ".legend-item { display: inline-block; margin-right: 15px; margin-bottom: 5px; } "
            ".critical-alert { background-color: #fff5f5; border-left: 4px solid #f56565; } "
            ".critical-high { background-color: #fed7d7; color: #c53030; font-weight: bold; } "
            ".critical-medium { background-color: #feebc8; color: #dd6b20; font-weight: bold; } "
            ".alert-summary { margin: 20px 0; padding: 15px; background-color: #667eea; color: white; border-radius: 8px; text-align: center; border: 2px solid #5a67d8; } "
        )

        html_content = f"""<!DOCTYPE html><html><head><meta charset="UTF-8"><title>业务监控</title><style>{css_styles}</style></head><body><div class="container">"""

        # 生成重点关注报表
        critical_alerts = self.generate_critical_alerts_report(cpu_results_table, response_time_results_table, 20.0)

        if critical_alerts:
            # 添加汇总信息
            high_count = sum(1 for alert in critical_alerts if alert['severity'] == 'high')
            medium_count = sum(1 for alert in critical_alerts if alert['severity'] == 'medium')
            cpu_count = sum(1 for alert in critical_alerts if alert['type'] == 'CPU使用率')
            rt_count = sum(1 for alert in critical_alerts if alert['type'] == '响应时间')

            html_content += f"<div class='alert-summary'><h2 style='margin: 0 0 10px 0; color: white;'>🚨 重点关注报表汇总</h2><p style='margin: 5px 0; font-size: 16px;'>发现 <strong>{len(critical_alerts)}</strong> 个异常项目（变化幅度超过30%）</p><p style='margin: 5px 0;'>🔴 高严重级: {high_count} 个 | 🟡 中等严重级: {medium_count} 个</p><p style='margin: 5px 0;'>🖥️ CPU相关: {cpu_count} 个 | ⚡ 响应时间相关: {rt_count} 个</p></div>"

            html_content += "<h2>📊 详细列表</h2>"
            html_content += "<table><tr><th>监控类型</th><th>服务名</th><th>环境</th><th>接口功能</th><th>变化前</th><th>变化后</th><th>变化幅度</th><th>严重程度</th></tr>"

            for alert in critical_alerts:
                # 确定严重程度颜色
                severity_class = "rt-worse" if alert['severity'] == 'high' else "rt-medium"
                severity_text = "🔴 高" if alert['severity'] == 'high' else "🟡 中"

                # 确定变化方向图标
                change_icon = "⬆️" if alert['change_rate'] > 0 else "⬇️"
                change_class = "rt-worse" if alert['change_rate'] > 0 else "rt-improved"

                # 格式化数值显示
                if alert['type'] == 'CPU使用率':
                    first_display = f"{alert['first_value']:.4f}"
                    second_display = f"{alert['second_value']:.4f}"
                else:
                    first_display = f"{alert['first_value']:.2f}{alert['unit']}"
                    second_display = f"{alert['second_value']:.2f}{alert['unit']}"

                # 接口名显示（缩短显示）
                interface_display = alert['interface_name'][:30] + '...' if len(alert['interface_name']) > 30 else alert['interface_name']

                html_content += f"<tr><td class='service-name'>{alert['type']}</td><td class='service-name'>{alert['service_name']}</td><td class='namespace namespace-{alert['namespace']}'>{alert['namespace']}</td><td class='interface-name'>{interface_display}</td><td>{first_display}</td><td>{second_display}</td><td class='{change_class}'>{change_icon} {abs(alert['change_rate']):.1f}%</td><td class='{severity_class}'>{severity_text}</td></tr>"

            html_content += "</table>"

            # 添加说明信息
            html_content += "<div style='margin: 15px 0; padding: 10px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;'><strong>📝 说明:</strong> 以上数据为最近2天的变化对比，变化幅度超过15%的项目需要重点关注。</div>"
        else:
            html_content += "<h2>✅ 无重点关注项目</h2><p style='color: #28a745; font-weight: bold;'>所有监控指标变化幅度均在正常范围内（<15%）。</p>"

        # CPU使用率表格 - 合并相同服务名的格式
        if cpu_results_table:
            html_content += "<h2>🖥️ CPU使用率（最近3天，按环境区分）</h2><table><tr><th class='service-name'>服务名</th><th class='namespace'>环境</th>"

            # 添加日期列
            for date_str in all_dates:
                html_content += f"<th>{date_str}<br><span class='cpu-minmax'>平均值（最大值/最小值）</span></th>"

            # 添加变化幅度列（最近2天对比）
            if len(all_dates) >= 2:
                html_content += "<th>变化幅度<br>(最近2天)</th>"

            html_content += "</tr>"

            # 按服务名和namespace排序显示数据
            for (service_name, namespace) in sorted(cpu_by_service_ns.keys()):
                service_data = cpu_by_service_ns[(service_name, namespace)]
                css_class = f"namespace-{namespace}"

                html_content += f"<tr class='{css_class}'><td class='service-name'>{service_name}</td><td class='namespace'>{namespace}</td>"

                # 添加每个日期的CPU数据
                for date_str in all_dates:
                    if date_str in service_data['dates']:
                        cpu_data = service_data['dates'][date_str]
                        avg_cpu = cpu_data['avg_cpu_usage']
                        max_cpu = cpu_data['max_cpu_usage']
                        min_cpu = cpu_data['min_cpu_usage']

                        cpu_css_class = "cpu-low"
                        if avg_cpu > 0.8:
                            cpu_css_class = "cpu-high"
                        elif avg_cpu > 0.5:
                            cpu_css_class = "cpu-medium"

                        cpu_display = f"<span class='cpu-avg'>{avg_cpu:.4f}</span><br><span class='cpu-minmax'>({max_cpu:.4f}/{min_cpu:.4f})</span>"
                        html_content += f"<td class='{cpu_css_class}'>{cpu_display}</td>"
                    else:
                        html_content += "<td>-</td>"

                # 添加变化幅度（最近2天对比）
                if len(all_dates) >= 2:
                    # 获取最近2天的数据
                    recent_dates = sorted(all_dates)[-2:]
                    if len(recent_dates) == 2 and recent_dates[0] in service_data['dates'] and recent_dates[1] in \
                            service_data['dates']:
                        first_value = service_data['dates'][recent_dates[0]]['avg_cpu_usage']
                        second_value = service_data['dates'][recent_dates[1]]['avg_cpu_usage']

                        if first_value > 0:
                            change_rate = (second_value - first_value) / first_value * 100
                            change_css_class = "rt-improved" if change_rate < 0 else "rt-worse" if change_rate > 0 else ""
                            change_icon = "⬇️" if change_rate < 0 else "⬆️" if change_rate > 0 else "➡️"
                            html_content += f"<td class='{change_css_class}'>{change_icon} {abs(change_rate):.2f}%</td>"
                        else:
                            html_content += "<td>-</td>"
                    else:
                        html_content += "<td>-</td>"
                else:
                    html_content += "<td>-</td>"

                html_content += "</tr>"

            html_content += "</table>"

        # 响应时间表格 - 优化后的格式
        if response_time_results_table and len(all_dates) >= 2:
            html_content += "<h2>⚡ 响应时间（最近3天，按环境区分）</h2><table><tr><th class='service-name'>服务名</th><th class='namespace'>环境</th><th class='interface-name'>接口功能</th>"

            # 添加日期列
            for date_str in all_dates:
                html_content += f"<th>{date_str}<br>平均值(ms)</th>"

            # 添加变化幅度列（最近2天对比）
            if len(all_dates) >= 2:
                html_content += "<th>变化幅度<br>(最近2天)</th>"

            html_content += "</tr>"

            # 按服务名、namespace和接口排序
            for (service_name, interface_name, namespace) in sorted(response_time_by_interface_ns.keys()):
                interface_data = response_time_by_interface_ns[(service_name, interface_name, namespace)]
                css_class = f"namespace-{namespace}"

                html_content += f"<tr class='{css_class}'><td class='service-name'>{service_name}</td><td class='namespace'>{namespace}</td><td class='interface-name'>{interface_name}</td>"

                # 添加每个日期的响应时间
                for date_str in all_dates:
                    if date_str in interface_data['dates']:
                        rt_data = interface_data['dates'][date_str]
                        avg_rt = rt_data['avg_response_time_ms']

                        rt_css_class = "rt-fast"
                        if avg_rt > 1000:  # 大于1秒
                            rt_css_class = "rt-slow"
                        elif avg_rt > 500:  # 大于500ms
                            rt_css_class = "rt-medium"

                        html_content += f"<td class='{rt_css_class}' title='平均: {avg_rt:.2f}ms, 最大: {rt_data['max_response_time_ms']:.2f}ms, 最小: {rt_data['min_response_time_ms']:.2f}ms'>{avg_rt:.2f}</td>"
                    else:
                        html_content += "<td>-</td>"

                # 添加变化幅度（最近2天对比）
                if len(all_dates) >= 2:
                    # 获取最近2天的数据
                    recent_dates = sorted(all_dates)[-2:]
                    if len(recent_dates) == 2 and recent_dates[0] in interface_data['dates'] and recent_dates[1] in \
                            interface_data['dates']:
                        first_value = interface_data['dates'][recent_dates[0]]['avg_response_time_ms']
                        second_value = interface_data['dates'][recent_dates[1]]['avg_response_time_ms']

                        if first_value > 0:
                            change_rate = (second_value - first_value) / first_value * 100
                            change_css_class = "rt-improved" if change_rate < 0 else "rt-worse" if change_rate > 0 else ""
                            change_icon = "⬇️" if change_rate < 0 else "⬆️" if change_rate > 0 else "➡️"
                            html_content += f"<td class='{change_css_class}'>{change_icon} {abs(change_rate):.2f}%</td>"
                        else:
                            html_content += "<td>-</td>"
                    else:
                        html_content += "<td>-</td>"
                else:
                    html_content += "<td>-</td>"

                html_content += "</tr>"

            html_content += "</table>"

        # 添加监控曲线图部分（7天数据）
        html_content += "<div class='chart-section'><h2>📈 监控曲线图（最近7天）</h2>"

        # 获取所有服务名（使用图表数据）
        all_services = set()
        if cpu_results_chart:
            all_services.update(result['service_name'] for result in cpu_results_chart)
        if response_time_results_chart:
            all_services.update(result['service_name'] for result in response_time_results_chart)

        # 为每个服务生成图表
        for service_name in sorted(all_services):
            html_content += f"<h3>🔧 {service_name}</h3>"
            html_content += "<div class='chart-container'>"

            # CPU使用率图表（7天数据）
            if cpu_results_chart:
                cpu_chart = self.generate_cpu_chart(cpu_results_chart, service_name)
                if cpu_chart:
                    html_content += f"<div class='chart-item'><img src='data:image/png;base64,{cpu_chart}' alt='CPU使用率趋势图'/></div>"

            # 响应时间图表（7天数据）
            if response_time_results_chart:
                rt_chart = self.generate_response_time_chart(response_time_results_chart, service_name)
                if rt_chart:
                    html_content += f"<div class='chart-item'><img src='data:image/png;base64,{rt_chart}' alt='响应时间趋势图'/></div>"

            html_content += "</div>"

        html_content += "</div>"

        # 图例说明
        html_content += (
            "<div class='legend'>"
            "<div><strong>📋 说明:</strong></div>"
            "<div class='legend-item'><span class='cpu-high'>🔴 高CPU使用率(>0.8)</span></div>"
            "<div class='legend-item'><span class='cpu-medium'>🟡 中等CPU使用率(>0.5)</span></div>"
            "<div class='legend-item'><span class='cpu-low'>🟢 低CPU使用率(≤0.5)</span></div><br>"
            "<div class='legend-item'><span class='rt-slow'>🔴 慢响应(>1s)</span></div>"
            "<div class='legend-item'><span class='rt-medium'>🟡 中等响应(>500ms)</span></div>"
            "<div class='legend-item'><span class='rt-fast'>🟢 快响应(≤500ms)</span></div><br>"
            "<div class='legend-item'><span class='rt-improved'>⬇️ 性能改善</span></div>"
            "<div class='legend-item'><span class='rt-worse'>⬆️ 性能下降</span></div><br>"
            "<div><strong>重点关注:</strong> 变化幅度超过30%的项目会在顶部单独展示，按严重程度排序</div>"
            "<div><strong>环境说明:</strong> gray=灰度环境, prod=生产环境</div>"
            "<div><strong>CPU格式:</strong> 平均值(最大值/最小值)</div>"
            "<div><strong>变化幅度:</strong> 对比最近2天的数据变化</div>"
            "<div><strong>统计时间段:</strong> 上午9点-11点</div>"
            "<div><strong>曲线图说明:</strong> 不同环境使用不同颜色和线型区分，gray环境使用实线和圆点，prod环境使用虚线和方块</div>"
            "<div><strong>CPU曲线:</strong> 实线为平均值，点线为最大/最小值，阴影区域为波动范围</div>"
            "<div><strong>响应时间曲线:</strong> 每个环境最多显示3个核心接口，避免图表过于复杂</div>"
            "</div></div></body></html>"
        )

        return html_content

    def send_combined_email_report(self, cpu_results_table, response_time_results_table, cpu_results_chart, response_time_results_chart, email_config):
        """
        发送包含CPU和响应时间的综合邮件报表
        cpu_results_table: 用于表格显示的CPU数据（3天）
        response_time_results_table: 用于表格显示的响应时间数据（3天）
        cpu_results_chart: 用于图表显示的CPU数据（7天）
        response_time_results_chart: 用于图表显示的响应时间数据（7天）
        """
        if not yagmail:
            logger.error("yagmail未安装，无法发送邮件")
            return False

        if not cpu_results_table and not response_time_results_table:
            logger.warning("没有数据，跳过邮件发送")
            return False

        try:
            # 生成综合HTML报表
            html_content = self.generate_combined_html_report(cpu_results_table, response_time_results_table, cpu_results_chart, response_time_results_chart)

            # 生成CSV附件
            attachments = []
            if email_config.get('attach_csv', True):
                # CPU数据CSV（使用表格数据）
                if cpu_results_table:
                    with tempfile.NamedTemporaryFile(mode='w', suffix='_cpu_3days.csv', delete=False, encoding='utf-8') as f:
                        cpu_csv_filename = f.name
                        import csv
                        fieldnames = ['service_name', 'date', 'avg_cpu_usage', 'max_cpu_usage', 'min_cpu_usage']
                        writer = csv.DictWriter(f, fieldnames=fieldnames)
                        writer.writeheader()
                        sorted_results = sorted(cpu_results_table, key=lambda x: (x['service_name'], x['date']))
                        for result in sorted_results:
                            writer.writerow(result)
                        attachments.append(cpu_csv_filename)

                # 响应时间数据CSV（使用表格数据）
                if response_time_results_table:
                    with tempfile.NamedTemporaryFile(mode='w', suffix='_response_time_3days.csv', delete=False, encoding='utf-8') as f:
                        rt_csv_filename = f.name
                        import csv
                        fieldnames = ['service_name', 'interface_name', 'date', 'avg_response_time_ms', 'max_response_time_ms', 'min_response_time_ms']
                        writer = csv.DictWriter(f, fieldnames=fieldnames)
                        writer.writeheader()
                        sorted_results = sorted(response_time_results_table, key=lambda x: (x['service_name'], x['date']))
                        for result in sorted_results:
                            writer.writerow(result)
                        attachments.append(rt_csv_filename)

            # 配置邮件
            yag = yagmail.SMTP(
                user=email_config['smtp_user'],
                password=email_config['smtp_password'],
                smtp_ssl=True,
                host=email_config.get('smtp_host', 'smtp.exmail.qq.com'),
                port=email_config.get('smtp_port', 465)
            )

            # 生成邮件主题
            today = datetime.now().strftime('%Y-%m-%d')
            subject = f"📊 Prometheus 业务监控报表 - {today}"

            # 发送邮件
            yag.send(
                to=email_config['to_emails'],
                cc=email_config.get('cc_emails', []),
                subject=subject,
                contents=html_content,
                attachments=attachments if attachments else None
            )

            logger.info(f"综合邮件发送成功，收件人: {email_config['to_emails']}")

            # 清理临时文件
            for filename in attachments:
                if os.path.exists(filename):
                    os.unlink(filename)

            return True

        except Exception as e:
            logger.error(f"综合邮件发送失败: {e}")
            return False

    def get_response_time_report(self, days=2):
        """
        获取最近指定天数每天9-11点的核心服务接口平均响应时间
        基于Spring Boot Micrometer指标，只查询核心业务接口，按namespace区分
        """
        # 用于存储所有原始数据的字典，key为(service_name, interface_name, namespace, date)
        raw_data = {}

        # 计算日期范围
        today = datetime.now().date()

        # 定义要查询的namespace
        namespaces = ['gray', 'prod']
        results = []
        for i in range(days):
            target_date = today - timedelta(days=i)

            # 设置时间范围：9:00-11:00
            start_datetime = datetime.combine(target_date, datetime.min.time().replace(hour=9))
            end_datetime = datetime.combine(target_date, datetime.min.time().replace(hour=11))

            # 转换为Unix时间戳
            start_timestamp = int(start_datetime.timestamp())
            end_timestamp = int(end_datetime.timestamp())

            logger.info(f"查询核心接口响应时间 - 日期: {target_date}, 时间范围: {start_datetime} - {end_datetime}")

            # 分别查询每个namespace的数据
            for namespace in namespaces:
                # 为每个核心接口构建查询
                for service_name, interface_desc, uri_path, method in core_interfaces:
                    # 构建针对特定服务、URI和namespace的查询
                    query = f'(sum(rate(http_server_requests_seconds_sum{{service="{service_name}", namespace="{namespace}", exception="None", uri="{uri_path}", method="{method}", status="200"}}[5m])) / sum(rate(http_server_requests_seconds_count{{service="{service_name}", namespace="{namespace}", exception="None", uri="{uri_path}", method="{method}", status="200"}}[5m]))) * 1000'

                    logger.info(f"查询接口: {service_name} namespace={namespace} - {interface_desc} ({uri_path})")
                    data = self.query_prometheus(query, start_timestamp, end_timestamp, '60s')

                    if not data or data.get('status') != 'success':
                        logger.warning(f"查询接口 {service_name}:{uri_path} namespace={namespace} 在 {target_date} 数据失败")
                        continue

                    # 处理查询结果
                    for result in data['data']['result']:
                        metric = result['metric']
                        values = result['values']

                        date_str = target_date.strftime('%Y-%m-%d')
                        interface_key = f"{interface_desc}({uri_path})"
                        ns = metric.get('namespace', namespace)  # 使用查询的namespace作为默认值

                        if not values:
                            logger.warning(f"接口 {service_name}:{interface_desc} namespace={ns} 在 {target_date} 无响应时间数据")
                            continue

                        # 提取有效的响应时间值（毫秒）
                        response_times = [float(value[1]) for value in values if value[1] != 'NaN' and float(value[1]) > 0]

                        if response_times:
                            key = (service_name, interface_key, ns, date_str)
                            if key not in raw_data:
                                raw_data[key] = []
                            raw_data[key].extend(response_times)

            # 将统计计算移到循环外部
            for (service_name, interface_key, namespace, date_str), all_response_times in raw_data.items():
                if all_response_times:
                    avg_response_time = sum(all_response_times) / len(all_response_times)
                    max_response_time = max(all_response_times)
                    min_response_time = min(all_response_times)

                    results.append({
                        'service_name': service_name,
                        'interface_name': interface_key,
                        'namespace': namespace,
                        'date': date_str,
                        'avg_response_time_ms': round(avg_response_time, 2),
                        'max_response_time_ms': round(max_response_time, 2),
                        'min_response_time_ms': round(min_response_time, 2),
                        'data_points': len(all_response_times)
                    })
                    logger.info(f"核心接口响应时间 - 服务: {service_name}, namespace: {namespace}, 接口: {interface_key}, 日期: {date_str}, 平均: {avg_response_time:.2f}ms")

        return results

    def generate_cpu_chart(self, cpu_results, service_name):
        """
        为指定服务生成CPU使用率趋势图，按namespace分别展示
        """
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 筛选指定服务的数据
        service_data = [result for result in cpu_results if result['service_name'] == service_name]
        if not service_data:
            return None

        # 按namespace分组数据
        namespace_data = defaultdict(list)
        for result in service_data:
            namespace_data[result['namespace']].append(result)

        # 创建图表
        plt.figure(figsize=(12, 8))

        # 定义颜色和样式
        colors = {'gray': '#2E86AB', 'prod': '#A23B72'}
        markers = {'gray': 'o', 'prod': 's'}
        line_styles = {'gray': '-', 'prod': '--'}

        # 为每个namespace绘制曲线
        for namespace in sorted(namespace_data.keys()):
            data = namespace_data[namespace]
            data.sort(key=lambda x: x['date'])

            dates = [datetime.strptime(result['date'], '%Y-%m-%d').date() for result in data]
            avg_cpu = [result['avg_cpu_usage'] for result in data]
            max_cpu = [result['max_cpu_usage'] for result in data]
            min_cpu = [result['min_cpu_usage'] for result in data]

            color = colors.get(namespace, '#666666')
            marker = markers.get(namespace, 'o')
            line_style = line_styles.get(namespace, '-')

            # 绘制平均值线条
            plt.plot(dates, avg_cpu, marker=marker, linewidth=2,
                    label=f'{namespace} - 平均值', color=color, linestyle=line_style)

            # 绘制最大值和最小值（较细的线条）
            plt.plot(dates, max_cpu, marker='^', linewidth=1,
                    label=f'{namespace} - 最大值', color=color, alpha=0.6, linestyle=':')
            plt.plot(dates, min_cpu, marker='v', linewidth=1,
                    label=f'{namespace} - 最小值', color=color, alpha=0.6, linestyle=':')

            # 填充区域（不同namespace使用不同透明度）
            alpha = 0.15 if namespace == 'gray' else 0.1
            plt.fill_between(dates, min_cpu, max_cpu, alpha=alpha, color=color)

        # 设置标题和标签
        plt.title(f'{service_name} - CPU使用率趋势 (7天) - 按环境区分', fontsize=14, fontweight='bold')
        plt.xlabel('日期', fontsize=12)
        plt.ylabel('CPU使用率', fontsize=12)

        # 设置日期格式
        date_format = mdates.DateFormatter('%m-%d')
        plt.gca().xaxis.set_major_formatter(date_format)
        plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=1))

        # 添加网格
        plt.grid(True, alpha=0.3)

        # 添加图例
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

        # 旋转日期标签
        plt.xticks(rotation=45)
        plt.tight_layout()

        # 转换为base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
        buffer.seek(0)
        img_base64 = b64encode(buffer.read()).decode('utf-8')
        plt.close()

        return img_base64

    def generate_response_time_chart(self, response_time_results, service_name):
        """
        为指定服务生成响应时间趋势图，按namespace分别展示
        """
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 筛选指定服务的数据
        service_data = [result for result in response_time_results if result['service_name'] == service_name]
        if not service_data:
            return None

        # 按namespace和接口分组数据
        namespace_interface_data = defaultdict(lambda: defaultdict(list))
        for result in service_data:
            namespace_interface_data[result['namespace']][result['interface_name']].append(result)

        # 创建图表
        plt.figure(figsize=(14, 10))

        # 定义颜色和样式
        namespace_colors = {'gray': '#2E86AB', 'prod': '#A23B72'}
        namespace_markers = {'gray': 'o', 'prod': 's'}
        namespace_line_styles = {'gray': '-', 'prod': '--'}

        # 接口颜色调色板
        interface_colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']

        legend_entries = []
        color_index = 0

        # 为每个namespace绘制曲线
        for namespace in sorted(namespace_interface_data.keys()):
            interface_data = namespace_interface_data[namespace]

            # 限制每个namespace最多显示3个接口，避免图表过于复杂
            interfaces = list(interface_data.keys())[:3]

            base_color = namespace_colors.get(namespace, '#666666')
            marker = namespace_markers.get(namespace, 'o')
            line_style = namespace_line_styles.get(namespace, '-')

            for i, interface_name in enumerate(interfaces):
                data = interface_data[interface_name]
                data.sort(key=lambda x: x['date'])

                dates = [datetime.strptime(result['date'], '%Y-%m-%d').date() for result in data]
                avg_times = [result['avg_response_time_ms'] for result in data]

                # 为不同接口使用略有不同的颜色色调
                if namespace == 'gray':
                    color = interface_colors[color_index % len(interface_colors)]
                else:
                    # prod环境使用更深的颜色
                    base_rgb = [int(interface_colors[color_index % len(interface_colors)][1:3], 16),
                               int(interface_colors[color_index % len(interface_colors)][3:5], 16),
                               int(interface_colors[color_index % len(interface_colors)][5:7], 16)]
                    darker_rgb = [max(0, int(c * 0.7)) for c in base_rgb]
                    color = f"#{darker_rgb[0]:02x}{darker_rgb[1]:02x}{darker_rgb[2]:02x}"

                # 缩短接口名称显示
                short_interface_name = interface_name[:25] + '...' if len(interface_name) > 25 else interface_name
                label = f'{namespace} - {short_interface_name}'

                plt.plot(dates, avg_times, marker=marker, linewidth=2,
                        label=label, color=color, linestyle=line_style, alpha=0.8)

                color_index += 1

        # 设置标题和标签
        plt.title(f'{service_name} - 响应时间趋势 (7天) - 按环境区分', fontsize=14, fontweight='bold')
        plt.xlabel('日期', fontsize=12)
        plt.ylabel('响应时间 (ms)', fontsize=12)

        # 设置日期格式
        date_format = mdates.DateFormatter('%m-%d')
        plt.gca().xaxis.set_major_formatter(date_format)
        plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=1))

        # 添加网格
        plt.grid(True, alpha=0.3)

        # 添加图例（放在右侧）
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)

        # 旋转日期标签
        plt.xticks(rotation=45)
        plt.tight_layout()

        # 转换为base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
        buffer.seek(0)
        img_base64 = b64encode(buffer.read()).decode('utf-8')
        plt.close()

        return img_base64

    def print_response_time_report(self, results):
        """
        打印核心接口响应时间报表
        """
        if not results:
            print("\n❌ 没有找到任何核心接口响应时间数据")
            return

        print("\n" + "=" * 100)
        print("📊 Prometheus 核心接口响应时间监控报表")
        print("=" * 100)
        print(f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 100)

        # 计算列宽
        max_service_name_len = max(len(result['service_name']) for result in results)
        max_interface_name_len = max(len(result['interface_name']) for result in results)
        service_col_width = max(20, min(max_service_name_len + 2, 40))
        interface_col_width = max(30, min(max_interface_name_len + 2, 60))

        # 打印表头
        header = f"{'服务名':<{service_col_width}} {'接口':<{interface_col_width}} {'日期':<12} {'平均响应时间(ms)':<16} {'最大响应时间(ms)':<16} {'最小响应时间(ms)':<16} {'数据点':<8}"
        print(header)
        print("-" * len(header))

        # 排序并显示
        sorted_results = sorted(results, key=lambda x: (x['service_name'], x['interface_name'], x['date']))

        current_service = None
        for result in sorted_results:
            if current_service != result['service_name']:
                if current_service is not None:
                    print("-" * len(header))
                current_service = result['service_name']

            # 响应时间状态标识
            if result['avg_response_time_ms'] > 1000:  # 大于1秒
                status_icon = "🔴"
            elif result['avg_response_time_ms'] > 500:  # 大于500ms
                status_icon = "🟡"
            else:
                status_icon = "🟢"

            service_display = f"{status_icon} {result['service_name']}"

            print(f"{service_display:<{service_col_width+2}} {result['interface_name']:<{interface_col_width}} {result['date']:<12} {result['avg_response_time_ms']:<16.2f} {result['max_response_time_ms']:<16.2f} {result['min_response_time_ms']:<16.2f} {result['data_points']:<8}")

        print("=" * len(header))
        print(f"📈 总计: {len(results)} 条记录")
        print(f"🔴 慢响应(>1s)  🟡 中等响应(>500ms)  🟢 快响应(≤500ms)")
        print("=" * 100)

def main():
    parser = argparse.ArgumentParser(description='业务监控报表')
    parser.add_argument('--prometheus-url', required=False,
                       default="https://cn-shanghai.arms.aliyuncs.com:9443/api/v1/prometheus/cc9088bc80fa0109f85996cb49ce9ff/1858502702438156/ced5f20a675da434bab4501993f588fa1/cn-shanghai",
                       help='Prometheus服务器地址 (例如: http://localhost:9090)')
    parser.add_argument('--token', required=False,
                       help='Prometheus服务器token',
                       default="eyJhbGciOiJIUzI1NiJ9.eyJleHAiOjIwNjQ0MDIyNjYsImlzcyI6Imh0dHA6Ly9hbGliYWJhY2xvdWQuY29tIiwiaWF0IjoxNzQ5MDQyMjY2LCJqdGkiOiJlZWI0NDM5NC0wZDk1LTQ3YjktYTIwYi1lYmQyZDJiN2RjZjkifQ.cOBqt8p3ifZM5f5Kf0D687tDv9UpNn1eKnPCjPgTvm4")
    parser.add_argument('--days', type=int, default=7,
                       help='查询最近几天的数据 (默认: 7)')
    parser.add_argument('--export-csv', action='store_true',
                       help='导出结果到CSV文件')
    parser.add_argument('--csv-filename',
                       help='指定CSV文件名')

    # 邮件相关参数
    parser.add_argument('--send-email', action='store_true',
                       help='发送邮件报表')
    parser.add_argument('--smtp-user',
                       default='<EMAIL>',
                       help='SMTP用户名 (默认: <EMAIL>)')
    parser.add_argument('--smtp-password',
                       default='ABC@2025q',
                       help='SMTP密码')
    parser.add_argument('--smtp-host',
                       default='smtp.exmail.qq.com',
                       help='SMTP服务器 (默认: smtp.exmail.qq.com)')
    parser.add_argument('--smtp-port', type=int,
                       default=465,
                       help='SMTP端口 (默认: 465)')
    # parser.add_argument('--to-emails', nargs='+',
    #                    default=['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
    #                          '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
    #                          '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
    #                          '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
    #                    help='收件人邮箱列表')
    # parser.add_argument('--cc-emails', nargs='+',
    #                    default=['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>','<EMAIL>', '<EMAIL>'],
    #                    help='抄送邮箱列表')
    parser.add_argument('--to-emails', nargs='+',
                       default=['<EMAIL>'],
                       help='收件人邮箱列表'),
    parser.add_argument('--cc-emails', nargs='+',
                       default=['<EMAIL>']),
    parser.add_argument('--attach-csv', action='store_true',
                       default=False,
                       help='是否附加CSV文件 (默认: True)')

    args = parser.parse_args()

    # 验证Prometheus URL
    if not args.prometheus_url.startswith(('http://', 'https://')):
        logger.error("Prometheus URL必须以http://或https://开头")
        sys.exit(1)

    try:
        # 创建监控实例
        monitor = PrometheusMonitor(args.prometheus_url, args.token)

        # 获取CPU监控数据（表格用3天，图表用7天）
        logger.info("开始查询最近3天的CPU使用率数据（表格用）...")
        cpu_results_table = monitor.get_cpu_usage_report(3)

        logger.info("开始查询最近7天的CPU使用率数据（图表用）...")
        cpu_results_chart = monitor.get_cpu_usage_report(7)

        # 获取响应时间监控数据（表格用3天，图表用7天）
        logger.info("开始查询最近3天的响应时间数据（表格用）...")
        response_time_results_table = monitor.get_response_time_report(3)

        logger.info("开始查询最近7天的响应时间数据（图表用）...")
        response_time_results_chart = monitor.get_response_time_report(7)

        # 打印报表（使用表格数据）
        if cpu_results_table:
            print("\n" + "=" * 50 + " CPU使用率报表（最近3天） " + "=" * 50)
            monitor.print_report(cpu_results_table)

        if response_time_results_table:
            print("\n" + "=" * 50 + " 响应时间报表（最近3天） " + "=" * 50)
            monitor.print_response_time_report(response_time_results_table)

        # 导出CSV（如果需要）
        if args.export_csv:
            if cpu_results_table:
                monitor.export_to_csv(cpu_results_table, args.csv_filename or 'cpu_report_3days.csv')
            if response_time_results_table:
                # 这里需要添加响应时间的CSV导出方法
                pass

        # 发送综合邮件（如果需要）
        if args.send_email and (cpu_results_table or response_time_results_table):
            email_config = {
                'smtp_user': args.smtp_user,
                'smtp_password': args.smtp_password,
                'smtp_host': args.smtp_host,
                'smtp_port': args.smtp_port,
                'to_emails': args.to_emails,
                'cc_emails': args.cc_emails,
                'attach_csv': args.attach_csv
            }

            logger.info("正在发送综合邮件报表...")
            if monitor.send_combined_email_report(cpu_results_table, response_time_results_table, cpu_results_chart, response_time_results_chart, email_config):
                logger.info("综合邮件报表发送成功！")
            else:
                logger.error("综合邮件报表发送失败！")

    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()