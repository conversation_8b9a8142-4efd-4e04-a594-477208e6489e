import re
from typing import Set, Optional, Tuple, List
from decimal import Decimal


# Assuming these classes are defined elsewhere in the Python equivalent of the Java project
# For now, we'll define simple placeholder classes.

class GoodsConst:
    class TraceableCodeType:
        UNKNOWN = 0
        DRUG_CODE = 1
        MATERIAL_GS1 = 10
        MATERIAL_MA = 11

    class DrugIdentificationCodeType:
        NO_CODE = 10
        NORMAL = 0


class TraceableCodeNoInfo:
    def __init__(self):
        self.no: Optional[str] = None
        self.traceable_code_type: int = GoodsConst.TraceableCodeType.UNKNOWN
        self.drug_identification_code: Optional[str] = None
        self.type: Optional[int] = None
        self.production_date: Optional[str] = None
        self.expiry_date: Optional[str] = None
        self.batch_no: Optional[str] = None
        self.serial_number: Optional[str] = None
        self.ali_status: Optional[int] = None
        self.ali_piece_num: Optional[Decimal] = None
        self.ali_piece_unit: Optional[str] = None
        self.ali_package_unit: Optional[str] = None


class TraceableCode:
    def __init__(self):
        self.no: Optional[str] = None
        self.traceable_code_no_info: Optional[TraceableCodeNoInfo] = None


class TraceCodeUtils:
    """
    药品标识码 ：用于标识特定于某种与药品上市许可持有人、生产企业、药品通用名、剂型、制剂规格和包装规格
    药品追溯码:对应的药品的唯一性代码 ，用于唯一标识药品各级销售包装单元的代码，由一列数字、字母和（或）符号组成 ，药品追溯码的20位数字具有特定的含义，‌它们共同构成了药品的唯一标识。‌
        第1个数字是8，‌这是药品的标志位，‌用于区分其他类型的物品。‌
        第2个数字区分监管码的级别，‌可能是一级码、‌二级码、‌三级码等。‌
        第3-7个数是资源码，‌代表产品的生产厂家、‌药品名称、‌规格、‌包装规则等信息。‌
        第8-16个数是流水号，‌描述药品的每一个独立包装。‌
        第17-20个数是随机生成的加密码。‌
        普药: 前两位81 83 84 85 86 87 88
        特药：89
        https://www.nmpa.gov.cn/directory/web/nmpa/images/ufq80tKpxre84La9udzA7b7WMjAxOcTqtdozMrrFuau45ri9vP4yLnBkZg==.pdf
    耗材追溯码:
        https://udi.idcode.net/applyfor?bd_vid=10024913773379856025
        https://udi.idcode.net/article/510
        https://udi.nmpa.gov.cn/download.html
        http://dynamic.gs1cn.org/office/uploadFileResource/upload/GS1%E9%80%9A%E7%94%A8%E8%A7%84%E8%8C%83V18.pdf
        MA
        国家码 ： https://www.kaggle.com/datasets/andradaolteanu/iso-country-codes-global
        MA 白皮书 https://www.idcode.org.cn/UploadFiles/20211209093147262.pdf
    """

    no_drug_identification_code_set: Set[str] = set()
    special_drug_identification_code_set: Set[str] = set()

    MEDICAL_TRACEABLE_CODE_REGREX = re.compile(r"^([0-9]{7})([0-9]{6,9})?([0-9]{4})?$")
    MEDICAL_TRACEABLE_CODE_ORDER_REGREX = re.compile(r"^([a-zA-Z0-9]{7})([a-zA-Z0-9]{6,121})$")
    MATERIAL_TRACEABLE_CODE_MA_REGREX = re.compile(r"^(MA\.[0-9]{1,3}\.M[0-9]+\.[0-9]{6}\.[0-9a-zA-Z-]+)(\..*)?$")
    MATERIAL_TRACEABLE_CODE_GS1_REGREX = re.compile(r"^01([0-9]{14})(.*)$")
    MATERIAL_TRACEABLE_CODE_GS1_IDFICATION_CODE = re.compile(r"^([0-9]{14})$")
    MATERIAL_TRACEABLE_CODE_MA_PREFIX = "MA."
    MATERIAL_TRACEABLE_CODE_GS1_PREFIX = "01"
    MATERIAL_TRACEABLE_CODE_MA_PRODUCTDATE = ".P"
    MATERIAL_TRACEABLE_CODE_GS1_PRODUCTDATE = "11"
    MATERIAL_TRACEABLE_CODE_MA_EXPIRYDATE = ".E"
    MATERIAL_TRACEABLE_CODE_GS1_EXPIRYDATE = "17"
    MATERIAL_TRACEABLE_CODE_MA_BATCHNO = ".L"
    MATERIAL_TRACEABLE_CODE_GS1_BATCHNO = "10"
    MATERIAL_TRACEABLE_CODE_MA_SERIALNUM = ".S"
    MATERIAL_TRACEABLE_CODE_GS1_SERIALNUM = "21"
    GS_DATE_LENGTH = 8

    # Static block equivalent
    special_drug_identification_code_set.add("HLJTEST")

    @staticmethod
    def drug_identification_code(drug_trace_code: Optional[str]) -> Optional[TraceableCodeNoInfo]:
        if not drug_trace_code:
            return None

        trace_info = TraceableCodeNoInfo()
        trace_info.no = drug_trace_code
        trace_info.traceable_code_type = GoodsConst.TraceableCodeType.UNKNOWN

        trim_trace_code = drug_trace_code.strip().upper()

        if not TraceCodeUtils.no_drug_identification_code_set:
            TraceCodeUtils.no_drug_identification_code_set.add("22000000000000000000")  # Jilin No Code Collection
            TraceCodeUtils.no_drug_identification_code_set.add("WSYM0000000000000000")  # Henan Zhengzhou No Code Collection
            TraceCodeUtils.no_drug_identification_code_set.add("WZSM0000000000000000")  # Henan Zhengzhou No Code Collection
            TraceCodeUtils.no_drug_identification_code_set.add("该药品无追溯码")  # Fujian Xiamen No Code Collection
            TraceCodeUtils.no_drug_identification_code_set.add("承诺无追溯码")  # Zhejiang Hangzhou No Code Collection
            TraceCodeUtils.no_drug_identification_code_set.add("WMYP")  # Chongqing Drug No Code Collection
            TraceCodeUtils.no_drug_identification_code_set.add("WMHC")  # Chongqing Consumable Jilin No Code Collection
            TraceCodeUtils.no_drug_identification_code_set.add("0")  # Chongqing Consumable Jilin No Code Collection
            TraceCodeUtils.no_drug_identification_code_set.add("37020020243700000066")  # Shandong Qingdao No Code
            TraceCodeUtils.no_drug_identification_code_set.add("37020000000000000000")  # Shandong Qingdao No Code
            TraceCodeUtils.no_drug_identification_code_set.add("88888888888888888888")  # Shandong Yantai No Code
            TraceCodeUtils.no_drug_identification_code_set.add("00000000000000000000")  # Shandong Jinan No Code
            TraceCodeUtils.no_drug_identification_code_set.add("00000000")  # Shandong Linyi No Code
            TraceCodeUtils.no_drug_identification_code_set.add("8HB00000000000000000")  # Hubei All
            TraceCodeUtils.no_drug_identification_code_set.add("MTSY_99999")  # Hunan All
            TraceCodeUtils.no_drug_identification_code_set.add("WM000000000000000000")  # Guizhou

        if trim_trace_code in TraceCodeUtils.no_drug_identification_code_set:
            trace_info.no = trim_trace_code
            trace_info.drug_identification_code = trim_trace_code
            trace_info.type = GoodsConst.DrugIdentificationCodeType.NO_CODE
            return trace_info

        # Input is drug identifier
        if len(trim_trace_code) == 7 and (trim_trace_code.isdigit() or trim_trace_code in TraceCodeUtils.special_drug_identification_code_set):
            trace_info.drug_identification_code = trim_trace_code
            trace_info.traceable_code_type = GoodsConst.TraceableCodeType.DRUG_CODE
            trace_info.type = GoodsConst.DrugIdentificationCodeType.NORMAL
            return trace_info

        matcher = None
        """
        Consumable traceability code
        National drug database data 3.3 million GS1; 400,000 MA
        """
        if trim_trace_code.startswith(TraceCodeUtils.MATERIAL_TRACEABLE_CODE_GS1_PREFIX):
            matcher = TraceCodeUtils.MATERIAL_TRACEABLE_CODE_GS1_REGREX.match(trim_trace_code)
            if matcher:
                trace_info.drug_identification_code = matcher.group(1)
                trace_info.traceable_code_type = GoodsConst.TraceableCodeType.MATERIAL_GS1
                trace_info.type = GoodsConst.DrugIdentificationCodeType.NORMAL
                find_part_two = None
                if matcher.group(2) is not None:
                    find_part_two = matcher.group(2)
                    trace_info.no = drug_trace_code  # Original code

                if not find_part_two:
                    return trace_info

                sub = find_part_two
                while True:
                    exist = TraceCodeUtils._find_gs_sub_string(sub)
                    if exist is None:
                        break

                    if exist[0] == TraceCodeUtils.MATERIAL_TRACEABLE_CODE_GS1_PRODUCTDATE:
                        trace_info.production_date = exist[1]
                    elif exist[0] == TraceCodeUtils.MATERIAL_TRACEABLE_CODE_GS1_EXPIRYDATE:
                        trace_info.expiry_date = exist[1]
                    elif exist[0] == TraceCodeUtils.MATERIAL_TRACEABLE_CODE_GS1_BATCHNO:
                        trace_info.batch_no = exist[1]
                    elif exist[0] == TraceCodeUtils.MATERIAL_TRACEABLE_CODE_GS1_SERIALNUM:
                        trace_info.serial_number = exist[1]
                    sub = exist[2]
                return trace_info

        elif trim_trace_code.startswith(TraceCodeUtils.MATERIAL_TRACEABLE_CODE_MA_PREFIX):
            matcher = TraceCodeUtils.MATERIAL_TRACEABLE_CODE_MA_REGREX.match(trim_trace_code)
            if matcher:
                trace_info.drug_identification_code = matcher.group(1)
                trace_info.traceable_code_type = GoodsConst.TraceableCodeType.MATERIAL_MA
                trace_info.type = GoodsConst.DrugIdentificationCodeType.NORMAL
                find_part_two = None
                if matcher.group(2) is not None:
                    find_part_two = matcher.group(2)

                if not find_part_two:
                    return trace_info

                sub = find_part_two
                while True:
                    exist = TraceCodeUtils._find_ma_sub_string(sub)
                    if exist is None:
                        break

                    if exist[0] == TraceCodeUtils.MATERIAL_TRACEABLE_CODE_MA_PRODUCTDATE:
                        trace_info.production_date = exist[1]
                    elif exist[0] == TraceCodeUtils.MATERIAL_TRACEABLE_CODE_MA_EXPIRYDATE:
                        trace_info.expiry_date = exist[1]
                    elif exist[0] == TraceCodeUtils.MATERIAL_TRACEABLE_CODE_MA_BATCHNO:
                        trace_info.batch_no = exist[1]
                    elif exist[0] == TraceCodeUtils.MATERIAL_TRACEABLE_CODE_MA_SERIALNUM:
                        trace_info.serial_number = exist[1]
                    sub = exist[2]
                return trace_info

        elif len(trim_trace_code) == 14 and trim_trace_code.isdigit():
            # User directly entered GS1 14-digit number in drug information
            trace_info.drug_identification_code = trim_trace_code
            trace_info.traceable_code_type = GoodsConst.TraceableCodeType.MATERIAL_GS1
            trace_info.type = GoodsConst.DrugIdentificationCodeType.NORMAL
            return trace_info

        # Remaining checks if it's greater than 13 digits
        matcher = TraceCodeUtils.MEDICAL_TRACEABLE_CODE_REGREX.match(trim_trace_code)
        if matcher:
            trace_info.drug_identification_code = matcher.group(1)
            trace_info.traceable_code_type = GoodsConst.TraceableCodeType.DRUG_CODE
            trace_info.type = GoodsConst.DrugIdentificationCodeType.NORMAL
            # If exists, get and print the content of the second capturing group
            if matcher.group(2) is not None:
                trace_info.serial_number = matcher.group(2)
            return trace_info

        matcher = TraceCodeUtils.MEDICAL_TRACEABLE_CODE_ORDER_REGREX.match(trim_trace_code)
        if matcher:
            trace_info.drug_identification_code = matcher.group(1)
            trace_info.traceable_code_type = GoodsConst.TraceableCodeType.DRUG_CODE
            trace_info.type = GoodsConst.DrugIdentificationCodeType.NORMAL
            # If exists, get and print the content of the second capturing group
            if matcher.group(2) is not None:
                trace_info.serial_number = matcher.group(2)
            return trace_info

        return None

    @staticmethod
    def patch_ali_trace_code_info(info: TraceableCodeNoInfo, ali_status: int, ali_status_name: str, ali_piece_num: Decimal, ali_piece_unit: str, ali_package_unit: str) -> TraceableCodeNoInfo:
        info.ali_status = ali_status
        info.ali_piece_num = ali_piece_num
        info.ali_piece_unit = ali_piece_unit
        info.ali_package_unit = ali_package_unit
        return info

    @staticmethod
    def _find_ma_sub_string(gs1_udi: str) -> Optional[Tuple[str, str, str]]:
        if not gs1_udi:
            return None

        if gs1_udi.startswith(TraceCodeUtils.MATERIAL_TRACEABLE_CODE_MA_PRODUCTDATE) and len(gs1_udi) >= TraceCodeUtils.GS_DATE_LENGTH:
            # YYMMDD ->20YY-MM-DD
            date = gs1_udi[2:TraceCodeUtils.GS_DATE_LENGTH]
            std_date = (f"19{date[0:2]}" if date >= "70" else f"20{date[0:2]}") + f"-{date[2:4]}-{date[4:6]}"
            left = gs1_udi[TraceCodeUtils.GS_DATE_LENGTH:]
            return TraceCodeUtils.MATERIAL_TRACEABLE_CODE_MA_PRODUCTDATE, std_date, left
        elif gs1_udi.startswith(TraceCodeUtils.MATERIAL_TRACEABLE_CODE_MA_EXPIRYDATE) and len(gs1_udi) >= TraceCodeUtils.GS_DATE_LENGTH:
            # YYMMDD ->20YY-MM-DD  19YY-MM-DD
            date = gs1_udi[2:TraceCodeUtils.GS_DATE_LENGTH]
            std_date = (f"19{date[0:2]}" if date >= "70" else f"20{date[0:2]}") + f"-{date[2:4]}-{date[4:6]}"
            left = gs1_udi[TraceCodeUtils.GS_DATE_LENGTH:]
            return TraceCodeUtils.MATERIAL_TRACEABLE_CODE_MA_EXPIRYDATE, std_date, left
        elif gs1_udi.startswith(TraceCodeUtils.MATERIAL_TRACEABLE_CODE_MA_BATCHNO):
            index = gs1_udi.find('.', 1)
            if index == -1:
                return TraceCodeUtils.MATERIAL_TRACEABLE_CODE_MA_BATCHNO, gs1_udi[2:], ""
            else:
                return TraceCodeUtils.MATERIAL_TRACEABLE_CODE_MA_BATCHNO, gs1_udi[2:index], gs1_udi[index:]
        elif gs1_udi.startswith(TraceCodeUtils.MATERIAL_TRACEABLE_CODE_MA_SERIALNUM):
            index = gs1_udi.find('.', 1)
            if index == -1:
                return TraceCodeUtils.MATERIAL_TRACEABLE_CODE_MA_SERIALNUM, gs1_udi[2:], ""
            else:
                return TraceCodeUtils.MATERIAL_TRACEABLE_CODE_MA_SERIALNUM, gs1_udi[2:index], gs1_udi[index:]
        else:
            index = gs1_udi.find('.', 1)
            if index != -1:
                # unknown
                return "UK", "", gs1_udi[index:]
        return None

    @staticmethod
    def _find_gs_sub_string(gs1_udi: str) -> Optional[Tuple[str, str, str]]:
        if not gs1_udi:
            return None

        if gs1_udi.startswith(TraceCodeUtils.MATERIAL_TRACEABLE_CODE_GS1_PRODUCTDATE) and len(gs1_udi) >= TraceCodeUtils.GS_DATE_LENGTH:
            # YYMMDD ->20YY-MM-DD
            date = gs1_udi[2:TraceCodeUtils.GS_DATE_LENGTH]
            std_date = (f"19{date[0:2]}" if date >= "70" else f"20{date[0:2]}") + f"-{date[2:4]}-{date[4:6]}"
            left = gs1_udi[TraceCodeUtils.GS_DATE_LENGTH:]
            return TraceCodeUtils.MATERIAL_TRACEABLE_CODE_GS1_PRODUCTDATE, std_date, left
        elif gs1_udi.startswith(TraceCodeUtils.MATERIAL_TRACEABLE_CODE_GS1_EXPIRYDATE) and len(gs1_udi) >= TraceCodeUtils.GS_DATE_LENGTH:
            # YYMMDD ->20YY-MM-DD  19YY-MM-DD
            date = gs1_udi[2:TraceCodeUtils.GS_DATE_LENGTH]
            std_date = (f"19{date[0:2]}" if date >= "70" else f"20{date[0:2]}") + f"-{date[2:4]}-{date[4:6]}"
            left = gs1_udi[TraceCodeUtils.GS_DATE_LENGTH:]
            return TraceCodeUtils.MATERIAL_TRACEABLE_CODE_GS1_EXPIRYDATE, std_date, left
        elif gs1_udi.startswith(TraceCodeUtils.MATERIAL_TRACEABLE_CODE_GS1_BATCHNO):
            count_21 = TraceCodeUtils._count_substring(gs1_udi, TraceCodeUtils.MATERIAL_TRACEABLE_CODE_GS1_SERIALNUM)
            if count_21 <= 0:
                return TraceCodeUtils.MATERIAL_TRACEABLE_CODE_GS1_BATCHNO, gs1_udi[2:], ""
            elif count_21 == 1:  # Not necessarily accurate, is 21 a separator or something else?
                index = gs1_udi.find(TraceCodeUtils.MATERIAL_TRACEABLE_CODE_GS1_SERIALNUM)
                if index < 8:  # Assume batch number is not less than 8, 21 is batch number
                    return TraceCodeUtils.MATERIAL_TRACEABLE_CODE_GS1_BATCHNO, gs1_udi[2:], ""
                else:
                    return TraceCodeUtils.MATERIAL_TRACEABLE_CODE_GS1_BATCHNO, gs1_udi[2:index], gs1_udi[index:]
            else:  # More than one 21
                index = gs1_udi.rfind(TraceCodeUtils.MATERIAL_TRACEABLE_CODE_GS1_SERIALNUM)
                return TraceCodeUtils.MATERIAL_TRACEABLE_CODE_GS1_BATCHNO, gs1_udi[2:index], gs1_udi[index:]
        elif gs1_udi.startswith(TraceCodeUtils.MATERIAL_TRACEABLE_CODE_GS1_SERIALNUM):
            return TraceCodeUtils.MATERIAL_TRACEABLE_CODE_GS1_SERIALNUM, gs1_udi[2:], ""
        return None

    @staticmethod
    def _count_substring(text: str, substring: str) -> int:
        count = 0
        index = 0
        while True:
            index = text.find(substring, index)
            if index == -1:
                break
            count += 1
            index += len(substring)
        return count

    @staticmethod
    def update_trace_code_drug_identification_code(traceable_code_list: List[TraceableCode]):
        if not traceable_code_list:
            return

        for traceable_code in traceable_code_list:
            if not traceable_code.no:
                continue
            traceable_code.traceable_code_no_info = TraceCodeUtils.drug_identification_code(traceable_code.no)
